{"editor.wordWrap": "on", "files.autoSave": "onFocusChange", "editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "eslint.format.enable": true, "editor.codeActionsOnSave": {"source.fixAll.stylelint": "explicit"}, "stylelint.validate": ["css", "scss", "less", "vue"], "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[scss]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[less]": {"editor.defaultFormatter": "vscode.css-language-features"}, "typescript.enablePromptUseWorkspaceTsdk": true}