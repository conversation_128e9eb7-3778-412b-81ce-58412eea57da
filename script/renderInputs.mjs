import { join } from 'path'
import appRootPath from 'app-root-path'
import shell from 'shelljs'
import cheerio from 'cheerio'
export const doc = `
# Multiple entry points + Prerender
- development mode example:
  - http://localhost:5173/src/module/demo/#/second
  - http://localhost:5173/#/second
- production mode example (in child module and prerender disabled):
  - http://127.0.0.1:5500/dist/demo/src/module/demo/#/
  - http://127.0.0.1:5500/dist/demo/src/module/demo/#/second
- production mode example (in child module and prerender enabled):
  - http://127.0.0.1:5500/dist/demo/#/
  - http://127.0.0.1:5500/dist/demo/re-second/#/second
- production mode example (in root module and whether prerender is on or off):
  - http://127.0.0.1:5500/dist/#/
  - http://127.0.0.1:5500/dist/#/second
`
export default function renderInputs() {
  const module = process.env.VITE_MODULE
  const inputPath = module === 'root' ? '/index.html' : `/src/module/${module}/index.html`
  const outputPath = module === 'root' ? '/dist' : `/dist/${module}`
  const output = join(appRootPath.path, outputPath)
  const input = join(appRootPath.path, inputPath)
  const prerenderRoutes = {
    // 配置需要预渲染的各个模块的子页面
    root: ['/'],
    demo: ['/', '/#/second', '/#/class']
  }
  shell.rm('-rf', output)
  return {
    vitePrerender: {
      // vitePrerender会在staticDir下启一个localhost:xxxx的服务，并在indexPath（不含/index.html）后拼接以下route
      staticDir: output,
      indexPath: join(output, inputPath),
      routes: prerenderRoutes[module],
      postProcess(renderedRoute) {
        // renderedRoute.route 是输出目录，下面是把/dddd/eee/中间的/转为-
        // nested/src/module/nested/index.html 输出目录改为 nested/xxx/index.html
        renderedRoute.route = renderedRoute.originalRoute
          .replace('#/', '')
          .replace(/^(\/)/, '<')
          .replace(/(\/)$/, '<')
          .replace(/\//g, '-')
          .replace(/</g, '/')
          .replace(/^(\/)(\w)/, '$1re-$2') //并添加一个表示重新渲染过的主文档的前缀
        const relativePath = renderedRoute.route === '/' ? './' : '../'
        // http://localhost:xxxx是vitePrerender临时起的服务
        renderedRoute.html = renderedRoute.html
          .replace(/\.\.\/\.\.\/\.\.\//g, relativePath)
          .replace(/http:\/\/localhost:[\d]+\//g, relativePath)
        //解析xml，把header中的js标签 移动到 div[id=app] 的下面
        const $ = cheerio.load(renderedRoute.html)
        $('html head script').insertAfter('div#app')
        renderedRoute.html = $.html()
        return renderedRoute
      }
    },
    rollupOptions: {
      input: {
        [module]: input
      },
      output: {
        dir: output
      }
    }
  }
}
