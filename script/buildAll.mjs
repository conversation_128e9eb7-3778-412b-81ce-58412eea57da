import { join } from 'path'
import appRootPath from 'app-root-path'
import shell from 'shelljs'

export default function buildAll() {
  let ret = 0
  ret += shell.exec(`npm run buildModule --module=root`).code

  const modulePath = join(appRootPath.path, '/src/module')
  let dirs = shell.ls('-A', [modulePath])
  dirs.forEach((dir) => {
    ret += shell.exec(`npm run buildModule --module=${dir}`).code
  })
  if (ret > 0) {
    shell.rm('-rf', join(appRootPath.path, '/dist'))
    throw new Error('build failed')
  }
  console.log(`build all modules successfully ! =================`)
}

buildAll()
