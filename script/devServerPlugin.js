import history from 'connect-history-api-fallback';
import fs from 'fs';
import path from 'path';

export function devServerPlugin() {
  const dirList = fs.readdirSync(path.join(__dirname, '../src/module/'));

  const rules = [];
  const multiPage = Object.fromEntries(
    dirList.filter((m) => !m.startsWith('.')).map((m) => [m, { name: m, rootPage: `/src/module/${m}/` }])
  );

  Reflect.ownKeys(multiPage).forEach((key) => {
    rules.push({
      from: `/${multiPage[key].name}`,
      to: `${multiPage[key].rootPage}`
    });
  });
  console.log(rules);
  return {
    name: 'path-rewrite-plugin',
    configureServer(server) {
      server.middlewares.use(
        history({
          // logger: console.log,
          htmlAcceptHeaders: ['text/html', 'application/xhtml+xml'],
          rewrites: rules
        })
      );
    }
  };
}
