import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue2'
import vueJsx from '@vitejs/plugin-vue2-jsx'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'
import vitePrerender from 'vite-plugin-prerender'
import renderInputs from './script/renderInputs.mjs'
import postcssPxToViewport from 'postcss-px-to-viewport'
import babel from 'vite-plugin-babel'
import { ViteEjsPlugin } from 'vite-plugin-ejs'
import { vanillaExtractPlugin } from '@vanilla-extract/vite-plugin'
import { devServerPlugin } from './script/devServerPlugin'
import fs from 'fs'
import { viteStaticCopy } from 'vite-plugin-static-copy'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode, ssrBuild }) => {
  process.env.VITE_MODULE = process.env.VITE_MODULE || 'root'

  process.env.VITE_BUILD_TYPE =
    process.env.VITE_BUILD_TYPE || (process.env.NODE_ENV === 'development' ? 'debug' : 'release')

  console.log(
    `============ start ${process.env.VITE_MODULE} module in ${process.env.VITE_BUILD_TYPE} ============\n`
  )

  const renderInputsConfig = renderInputs()
  const babelPlugins = [
    ['@babel/plugin-proposal-decorators', { legacy: true }],
    [
      '@nutui/babel-plugin-separate-import',
      {
        libraryName: '@nutui/nutui-jdl',
        libraryDirectory: 'dist/packages',
        style: 'scss'
      }
    ]
  ]
  const moduleDefaultOpenPath =
    process.env.VITE_MODULE === 'all' || process.env.VITE_MODULE === 'root'
      ? ''
      : `src/module/${process.env.VITE_MODULE}/index.html`

  return {
    plugins: [
      viteStaticCopy({
        targets: [
          {
            src: `./src/module/${process.env.VITE_MODULE}/public/*`,
            dest: `${process.env.VITE_MODULE}` // 复制到 dist 目录中的路径
          }
        ]
      }),
      vue(),
      vueJsx({ babelPlugins }),
      legacy({
        targets: ['Android >= 4.4', 'iOS >= 9'],
        modernPolyfills: true
      }),
      vitePrerender(renderInputsConfig.vitePrerender),
      babel({
        babelConfig: {
          babelrc: false,
          configFile: false,
          plugins: babelPlugins
        }
      }),
      ViteEjsPlugin((viteConfig) => ({
        env: { ...viteConfig.env, ...process.env }
      })),
      vanillaExtractPlugin(),
      devServerPlugin()
    ],
    server: {
      host: '0.0.0.0',
      open: process.env.VITE_MODULE !== 'root' ? `/src/module/${process.env.VITE_MODULE}/index.html` : true,
      proxy: {
        '/gateway': {
          target: 'https://fat-ap.cashcerdas.id',
          // target: 'http://*************:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/gateway/, '')
        },
        '/originApi': {
          // target: 'https://api.cashcerdas.id', // live proxy
          target: 'http://fat-api.cashcerdas.id/api', // fat proxy
          // target: 'http://************:8080'
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/originApi/, '')
        }
      },
      // use https on localhost
      https: {
        key: fs.readFileSync('localnet.key'),
        cert: fs.readFileSync('localnet.crt')
      }
    },
    // 打包后以相对路径导入静态资源
    base: './',
    build: {
      rollupOptions: renderInputsConfig.rollupOptions
    },
    // vite打包时可以正确识别@
    resolve: {
      alias: [{ find: '@', replacement: resolve(__dirname, 'src') }]
    },
    css: {
      preprocessorOptions: {
        requireModuleExtension: true,
        scss: {
          additionalData: `
            @use "sass:math";
            @import "@/common/styles/variables.scss";
            @import "@nutui/nutui-jdl/dist/styles/index.scss";
            @import "@nutui/nutui/dist/styles/index.scss";
          `
        }
      },
      postcss: {
        plugins: [
          // 可以使用大写的PX来忽略下边插件的影响
          postcssPxToViewport({
            viewportWidth: 360, // 视窗的宽度，对应的是我们设计稿的宽度.
            viewportHeight: 640, // 视窗的高度，对应的是我们设计稿的高度.(也可以不配置)
            unitPrecision: 5, // 保留几位小数，指定`px`转换为视窗单位值的小数位数（很多时候无法整除）
            viewportUnit: 'vw', // 指定需要转换成的视窗单位，建议使用vw
            selectorBlackList: [], // 指定不需要转换的类
            minPixelValue: 1, // 小于或等于`1px`不转换为视窗单位.
            mediaQuery: false, // 允许在媒体查询中转换`px`
            propList: ['*']
          })
        ]
      }
    },
    define: {
      'import.meta.env.VITE_MODULE': `'${process.env.VITE_MODULE}'`,
      'import.meta.env.VITE_BUILD_TYPE': `'${process.env.VITE_BUILD_TYPE}'`
    }
  }
})
