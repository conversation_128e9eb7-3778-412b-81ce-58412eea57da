- 1、安装依赖
```
"@vitejs/plugin-vue2-jsx": "^1.1.0",
"vue-tsx-support": "^3.2.0"
"vue-property-decorator": "^9.1.2",
"@babel/plugin-proposal-decorators": "^7.22.5",
```
- 2、tsconfig配置
```
"jsx": "preserve",
```

- 3、viteconfig 配置
```
import vueJsx from '@vitejs/plugin-vue2-jsx'
vueJsx({
        babelPlugins: [['@babel/plugin-proposal-decorators', { legacy: true }]]
}),
```

- 4、案例
```
import { Component, Prop } from 'vue-property-decorator'
import * as tsx from 'vue-tsx-support'

@Component
export default class App extends tsx.Component<{ a: 1 }> {
  render() {
    return <div>sdfsdf</div>
  }
}
```
