/**!
  * Description: oppo-fin-jsapi.js
  * Version: 2.6.8
  * Build Time: 2024-11-25 09:39:56
  */
!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(t="undefined"!=typeof globalThis?globalThis:t||self).FinJSBridge=r()}(this,(function(){var t=function(r,e){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])},t(r,e)};var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=Array.isArray,n="object"==typeof r&&r&&r.Object===Object&&r,o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")(),u=i.Symbol,a=Object.prototype,c=a.hasOwnProperty,f=a.toString,s=u?u.toStringTag:void 0;var l=Object.prototype.toString;var v=function(t){var r=c.call(t,s),e=t[s];try{t[s]=void 0;var n=!0}catch(t){}var o=f.call(t);return n&&(r?t[s]=e:delete t[s]),o},p=function(t){return l.call(t)},h=u?u.toStringTag:void 0;var d=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":h&&h in Object(t)?v(t):p(t)};var b=function(t){return null!=t&&"object"==typeof t},y=d,_=b;var g=function(t){return"symbol"==typeof t||_(t)&&"[object Symbol]"==y(t)},j=e,w=g,m=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,O=/^\w*$/;var S=function(t,r){if(j(t))return!1;var e=typeof t;return!("number"!=e&&"symbol"!=e&&"boolean"!=e&&null!=t&&!w(t))||(O.test(t)||!m.test(t)||null!=r&&t in Object(r))};var z=function(t){var r=typeof t;return null!=t&&("object"==r||"function"==r)},A=d,x=z;var N,I=function(t){if(!x(t))return!1;var r=A(t);return"[object Function]"==r||"[object GeneratorFunction]"==r||"[object AsyncFunction]"==r||"[object Proxy]"==r},B=i["__core-js_shared__"],E=(N=/[^.]+$/.exec(B&&B.keys&&B.keys.IE_PROTO||""))?"Symbol(src)_1."+N:"";var k=Function.prototype.toString;var R=function(t){if(null!=t){try{return k.call(t)}catch(t){}try{return t+""}catch(t){}}return""},M=I,P=function(t){return!!E&&E in t},T=z,C=R,$=/^\[object .+?Constructor\]$/,q=RegExp("^"+Function.prototype.toString.call(Object.prototype.hasOwnProperty).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var D=function(t){return!(!T(t)||P(t))&&(M(t)?q:$).test(C(t))},F=function(t,r){return null==t?void 0:t[r]};var U=function(t,r){var e=F(t,r);return D(e)?e:void 0},G=U(Object,"create"),J=G;var W=G,L=Object.prototype.hasOwnProperty;var V=G,H=Object.prototype.hasOwnProperty;var K=G;var Z=function(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r},Q=function(t){var r=this.__data__;if(W){var e=r[t];return"__lodash_hash_undefined__"===e?void 0:e}return L.call(r,t)?r[t]:void 0},X=function(t){var r=this.__data__;return V?void 0!==r[t]:H.call(r,t)},Y=function(t,r){var e=this.__data__;return this.size+=this.has(t)?0:1,e[t]=K&&void 0===r?"__lodash_hash_undefined__":r,this};function tt(t){var r=-1,e=null==t?0:t.length;for(this.clear();++r<e;){var n=t[r];this.set(n[0],n[1])}}tt.prototype.clear=function(){this.__data__=J?J(null):{},this.size=0},tt.prototype.delete=Z,tt.prototype.get=Q,tt.prototype.has=X,tt.prototype.set=Y;var rt=tt;var et=function(t,r){return t===r||t!=t&&r!=r},nt=et;var ot=function(t,r){for(var e=t.length;e--;)if(nt(t[e][0],r))return e;return-1},it=ot,ut=Array.prototype.splice;var at=ot;var ct=ot;var ft=ot;var st=function(t){var r=this.__data__,e=it(r,t);return e>=0&&(e==r.length-1?r.pop():ut.call(r,e,1),--this.size,!0)},lt=function(t){var r=this.__data__,e=at(r,t);return 0>e?void 0:r[e][1]},vt=function(t){return ct(this.__data__,t)>-1},pt=function(t,r){var e=this.__data__,n=ft(e,t);return 0>n?(++this.size,e.push([t,r])):e[n][1]=r,this};function ht(t){var r=-1,e=null==t?0:t.length;for(this.clear();++r<e;){var n=t[r];this.set(n[0],n[1])}}ht.prototype.clear=function(){this.__data__=[],this.size=0},ht.prototype.delete=st,ht.prototype.get=lt,ht.prototype.has=vt,ht.prototype.set=pt;var dt=ht,bt=U(i,"Map"),yt=rt,_t=dt,gt=bt;var jt=function(t){var r=typeof t;return"string"==r||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==t:null===t};var wt=function(t,r){var e=t.__data__;return jt(r)?e["string"==typeof r?"string":"hash"]:e.map},mt=wt;var Ot=wt;var St=wt;var zt=wt;var At=function(t,r){var e=zt(this,t),n=e.size;return e.set(t,r),this.size+=e.size==n?0:1,this},xt=function(t){var r=mt(this,t).delete(t);return this.size-=r?1:0,r},Nt=function(t){return Ot(this,t).get(t)},It=function(t){return St(this,t).has(t)},Bt=At;function Et(t){var r=-1,e=null==t?0:t.length;for(this.clear();++r<e;){var n=t[r];this.set(n[0],n[1])}}Et.prototype.clear=function(){this.size=0,this.__data__={hash:new yt,map:new(gt||_t),string:new yt}},Et.prototype.delete=xt,Et.prototype.get=Nt,Et.prototype.has=It,Et.prototype.set=Bt;var kt=Et,Rt=kt;function Mt(t,r){if("function"!=typeof t||null!=r&&"function"!=typeof r)throw new TypeError("Expected a function");var e=function(){var n=arguments,o=r?r.apply(this,n):n[0],i=e.cache;if(i.has(o))return i.get(o);var u=t.apply(this,n);return e.cache=i.set(o,u)||i,u};return e.cache=new(Mt.Cache||Rt),e}Mt.Cache=Rt;var Pt=Mt;var Tt=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ct=/\\(\\)?/g,$t=function(t){var r=Pt(t,(function(t){return 500===e.size&&e.clear(),t})),e=r.cache;return r}((function(t){var r=[];return 46===t.charCodeAt(0)&&r.push(""),t.replace(Tt,(function(t,e,n,o){r.push(n?o.replace(Ct,"$1"):e||t)})),r}));var qt=function(t,r){for(var e=-1,n=null==t?0:t.length,o=Array(n);++e<n;)o[e]=r(t[e],e,t);return o},Dt=qt,Ft=e,Ut=g,Gt=u?u.prototype:void 0,Jt=Gt?Gt.toString:void 0;var Wt=function t(r){if("string"==typeof r)return r;if(Ft(r))return Dt(r,t)+"";if(Ut(r))return Jt?Jt.call(r):"";var e=r+"";return"0"==e&&1/r==-Infinity?"-0":e},Lt=Wt;var Vt=function(t){return null==t?"":Lt(t)},Ht=e,Kt=S,Zt=$t,Qt=Vt;var Xt=function(t,r){return Ht(t)?t:Kt(t,r)?[t]:Zt(Qt(t))},Yt=g;var tr=function(t){if("string"==typeof t||Yt(t))return t;var r=t+"";return"0"==r&&1/t==-Infinity?"-0":r},rr=Xt,er=tr;var nr=function(t,r){for(var e=0,n=(r=rr(r,t)).length;null!=t&&n>e;)t=t[er(r[e++])];return e&&e==n?t:void 0},or=nr;var ir=function(t,r,e){var n=null==t?void 0:or(t,r);return void 0===n?e:n},ur=Object.prototype.hasOwnProperty;var ar=function(t,r){return null!=t&&ur.call(t,r)},cr=d,fr=b;var sr=function(t){return fr(t)&&"[object Arguments]"==cr(t)},lr=b,vr=Object.prototype,pr=vr.hasOwnProperty,hr=vr.propertyIsEnumerable,dr=sr(function(){return arguments}())?sr:function(t){return lr(t)&&pr.call(t,"callee")&&!hr.call(t,"callee")},br=dr,yr=/^(?:0|[1-9]\d*)$/;var _r=function(t,r){var e=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==e||"symbol"!=e&&yr.test(t))&&t>-1&&t%1==0&&r>t};var gr=function(t){return"number"==typeof t&&t>-1&&t%1==0&&9007199254740991>=t},jr=Xt,wr=br,mr=e,Or=_r,Sr=gr,zr=tr;var Ar=function(t,r,e){for(var n=-1,o=(r=jr(r,t)).length,i=!1;++n<o;){var u=zr(r[n]);if(!(i=null!=t&&e(t,u)))break;t=t[u]}return i||++n!=o?i:!!(o=null==t?0:t.length)&&Sr(o)&&Or(u,o)&&(mr(t)||wr(t))},xr=ar,Nr=Ar;var Ir=function(t,r){return null!=t&&Nr(t,r,xr)};var Br=function(t,r){for(var e=-1,n=null==t?0:t.length;++e<n&&!1!==r(t[e],e,t););return t};var Er=function(t){return function(r,e,n){for(var o=-1,i=Object(r),u=n(r),a=u.length;a--;){var c=u[t?a:++o];if(!1===e(i[c],c,i))break}return r}}();var kr=function(t,r){for(var e=-1,n=Array(t);++e<t;)n[e]=r(e);return n},Rr={exports:{}};var Mr=function(){return!1};!function(t,r){var e=r&&!r.nodeType&&r,n=e&&t&&!t.nodeType&&t,o=n&&n.exports===e?i.Buffer:void 0;t.exports=(o?o.isBuffer:void 0)||Mr}(Rr,Rr.exports);var Pr=d,Tr=gr,Cr=b,$r={};$r["[object Float32Array]"]=$r["[object Float64Array]"]=$r["[object Int8Array]"]=$r["[object Int16Array]"]=$r["[object Int32Array]"]=$r["[object Uint8Array]"]=$r["[object Uint8ClampedArray]"]=$r["[object Uint16Array]"]=$r["[object Uint32Array]"]=!0,$r["[object Arguments]"]=$r["[object Array]"]=$r["[object ArrayBuffer]"]=$r["[object Boolean]"]=$r["[object DataView]"]=$r["[object Date]"]=$r["[object Error]"]=$r["[object Function]"]=$r["[object Map]"]=$r["[object Number]"]=$r["[object Object]"]=$r["[object RegExp]"]=$r["[object Set]"]=$r["[object String]"]=$r["[object WeakMap]"]=!1;var qr=function(t){return Cr(t)&&Tr(t.length)&&!!$r[Pr(t)]};var Dr=function(t){return function(r){return t(r)}},Fr={exports:{}};!function(t,r){var e=r&&!r.nodeType&&r,o=e&&t&&!t.nodeType&&t,i=o&&o.exports===e&&n.process,u=function(){try{var t=o&&o.require&&o.require("util").types;return t||i&&i.binding&&i.binding("util")}catch(t){}}();t.exports=u}(Fr,Fr.exports);var Ur=qr,Gr=Fr.exports,Jr=Gr&&Gr.isTypedArray,Wr=Jr?Dr(Jr):Ur,Lr=kr,Vr=br,Hr=e,Kr=Rr.exports,Zr=_r,Qr=Wr,Xr=Object.prototype.hasOwnProperty;var Yr=function(t,r){var e=Hr(t),n=!e&&Vr(t),o=!e&&!n&&Kr(t),i=!e&&!n&&!o&&Qr(t),u=e||n||o||i,a=u?Lr(t.length,String):[],c=a.length;for(var f in t)!r&&!Xr.call(t,f)||u&&("length"==f||o&&("offset"==f||"parent"==f)||i&&("buffer"==f||"byteLength"==f||"byteOffset"==f)||Zr(f,c))||a.push(f);return a},te=Object.prototype;var re=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||te)};var ee=function(t,r){return function(e){return t(r(e))}}(Object.keys,Object),ne=re,oe=ee,ie=Object.prototype.hasOwnProperty;var ue=function(t){if(!ne(t))return oe(t);var r=[];for(var e in Object(t))ie.call(t,e)&&"constructor"!=e&&r.push(e);return r},ae=I,ce=gr;var fe=function(t){return null!=t&&ce(t.length)&&!ae(t)},se=Yr,le=ue,ve=fe;var pe=function(t){return ve(t)?se(t):le(t)},he=Er,de=pe;var be=fe;var ye=function(t,r){return function(e,n){if(null==e)return e;if(!be(e))return t(e,n);for(var o=e.length,i=r?o:-1,u=Object(e);(r?i--:++i<o)&&!1!==n(u[i],i,u););return e}}((function(t,r){return t&&he(t,r,de)}));var _e=function(t){return t},ge=_e;var je=Br,we=ye,me=function(t){return"function"==typeof t?t:ge},Oe=e;var Se=function(t,r){return(Oe(t)?je:we)(t,me(r))},ze=dt;var Ae=dt,xe=bt,Ne=kt;var Ie=dt,Be=function(t){var r=this.__data__,e=r.delete(t);return this.size=r.size,e},Ee=function(t){return this.__data__.get(t)},ke=function(t){return this.__data__.has(t)},Re=function(t,r){var e=this.__data__;if(e instanceof Ae){var n=e.__data__;if(!xe||199>n.length)return n.push([t,r]),this.size=++e.size,this;e=this.__data__=new Ne(n)}return e.set(t,r),this.size=e.size,this};function Me(t){var r=this.__data__=new Ie(t);this.size=r.size}Me.prototype.clear=function(){this.__data__=new ze,this.size=0},Me.prototype.delete=Be,Me.prototype.get=Ee,Me.prototype.has=ke,Me.prototype.set=Re;var Pe=Me;var Te=kt,Ce=function(t){return this.__data__.has(t)};function $e(t){var r=-1,e=null==t?0:t.length;for(this.__data__=new Te;++r<e;)this.add(t[r])}$e.prototype.add=$e.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},$e.prototype.has=Ce;var qe=$e,De=function(t,r){for(var e=-1,n=null==t?0:t.length;++e<n;)if(r(t[e],e,t))return!0;return!1},Fe=function(t,r){return t.has(r)};var Ue=function(t,r,e,n,o,i){var u=1&e,a=t.length,c=r.length;if(!(a==c||u&&c>a))return!1;var f=i.get(t);if(f&&i.get(r))return f==r;var s=-1,l=!0,v=2&e?new qe:void 0;for(i.set(t,r),i.set(r,t);++s<a;){var p=t[s],h=r[s];if(n)var d=u?n(h,p,s,r,t,i):n(p,h,s,t,r,i);if(void 0!==d){if(d)continue;l=!1;break}if(v){if(!De(r,(function(t,r){if(!Fe(v,r)&&(p===t||o(p,t,e,n,i)))return v.push(r)}))){l=!1;break}}else if(p!==h&&!o(p,h,e,n,i)){l=!1;break}}return i.delete(t),i.delete(r),l};var Ge=i.Uint8Array,Je=et,We=Ue,Le=function(t){var r=-1,e=Array(t.size);return t.forEach((function(t,n){e[++r]=[n,t]})),e},Ve=function(t){var r=-1,e=Array(t.size);return t.forEach((function(t){e[++r]=t})),e},He=u?u.prototype:void 0,Ke=He?He.valueOf:void 0;var Ze=function(t,r,e,n,o,i,u){switch(e){case"[object DataView]":if(t.byteLength!=r.byteLength||t.byteOffset!=r.byteOffset)return!1;t=t.buffer,r=r.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=r.byteLength||!i(new Ge(t),new Ge(r)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Je(+t,+r);case"[object Error]":return t.name==r.name&&t.message==r.message;case"[object RegExp]":case"[object String]":return t==r+"";case"[object Map]":var a=Le;case"[object Set]":if(a||(a=Ve),t.size!=r.size&&!(1&n))return!1;var c=u.get(t);if(c)return c==r;n|=2,u.set(t,r);var f=We(a(t),a(r),n,o,i,u);return u.delete(t),f;case"[object Symbol]":if(Ke)return Ke.call(t)==Ke.call(r)}return!1};var Qe=function(t,r){for(var e=-1,n=r.length,o=t.length;++e<n;)t[o+e]=r[e];return t},Xe=Qe,Ye=e;var tn=function(t,r){for(var e=-1,n=null==t?0:t.length,o=0,i=[];++e<n;){var u=t[e];r(u,e,t)&&(i[o++]=u)}return i},rn=Object.prototype.propertyIsEnumerable,en=Object.getOwnPropertySymbols,nn=function(t,r,e){var n=r(t);return Ye(t)?n:Xe(n,e(t))},on=en?function(t){return null==t?[]:tn(en(t=Object(t)),(function(r){return rn.call(t,r)}))}:function(){return[]},un=pe;var an=function(t){return nn(t,un,on)},cn=Object.prototype.hasOwnProperty;var fn=function(t,r,e,n,o,i){var u=1&e,a=an(t),c=a.length;if(c!=an(r).length&&!u)return!1;for(var f=c;f--;){var s=a[f];if(!(u?s in r:cn.call(r,s)))return!1}var l=i.get(t);if(l&&i.get(r))return l==r;var v=!0;i.set(t,r),i.set(r,t);for(var p=u;++f<c;){var h=t[s=a[f]],d=r[s];if(n)var b=u?n(d,h,s,r,t,i):n(h,d,s,t,r,i);if(!(void 0===b?h===d||o(h,d,e,n,i):b)){v=!1;break}p||(p="constructor"==s)}if(v&&!p){var y=t.constructor,_=r.constructor;y==_||!("constructor"in t)||!("constructor"in r)||"function"==typeof y&&y instanceof y&&"function"==typeof _&&_ instanceof _||(v=!1)}return i.delete(t),i.delete(r),v},sn=U(i,"DataView"),ln=bt,vn=U(i,"Promise"),pn=U(i,"Set"),hn=U(i,"WeakMap"),dn=d,bn=R,yn="[object Map]",_n="[object Promise]",gn="[object Set]",jn="[object WeakMap]",wn="[object DataView]",mn=bn(sn),On=bn(ln),Sn=bn(vn),zn=bn(pn),An=bn(hn),xn=dn;(sn&&xn(new sn(new ArrayBuffer(1)))!=wn||ln&&xn(new ln)!=yn||vn&&xn(vn.resolve())!=_n||pn&&xn(new pn)!=gn||hn&&xn(new hn)!=jn)&&(xn=function(t){var r=dn(t),e="[object Object]"==r?t.constructor:void 0,n=e?bn(e):"";if(n)switch(n){case mn:return wn;case On:return yn;case Sn:return _n;case zn:return gn;case An:return jn}return r});var Nn=xn,In=Pe,Bn=Ue,En=Ze,kn=fn,Rn=Nn,Mn=e,Pn=Rr.exports,Tn=Wr,Cn="[object Arguments]",$n="[object Array]",qn="[object Object]",Dn=Object.prototype.hasOwnProperty;var Fn=function(t,r,e,n,o,i){var u=Mn(t),a=Mn(r),c=u?$n:Rn(t),f=a?$n:Rn(r),s=(c=c==Cn?qn:c)==qn,l=(f=f==Cn?qn:f)==qn,v=c==f;if(v&&Pn(t)){if(!Pn(r))return!1;u=!0,s=!1}if(v&&!s)return i||(i=new In),u||Tn(t)?Bn(t,r,e,n,o,i):En(t,r,c,e,n,o,i);if(!(1&e)){var p=s&&Dn.call(t,"__wrapped__"),h=l&&Dn.call(r,"__wrapped__");if(p||h){var d=p?t.value():t,b=h?r.value():r;return i||(i=new In),o(d,b,e,n,i)}}return!!v&&(i||(i=new In),kn(t,r,e,n,o,i))},Un=b;var Gn=function t(r,e,n,o,i){return r===e||(null==r||null==e||!Un(r)&&!Un(e)?r!=r&&e!=e:Fn(r,e,n,o,t,i))},Jn=Pe,Wn=Gn;var Ln=z;var Vn=function(t){return t==t&&!Ln(t)},Hn=Vn,Kn=pe;var Zn=function(t,r){return function(e){return null!=e&&(e[t]===r&&(void 0!==r||t in Object(e)))}},Qn=function(t,r,e,n){var o=e.length,i=o,u=!n;if(null==t)return!i;for(t=Object(t);o--;){var a=e[o];if(u&&a[2]?a[1]!==t[a[0]]:!(a[0]in t))return!1}for(;++o<i;){var c=(a=e[o])[0],f=t[c],s=a[1];if(u&&a[2]){if(void 0===f&&!(c in t))return!1}else{var l=new Jn;if(n)var v=n(f,s,c,t,r,l);if(!(void 0===v?Wn(s,f,3,n,l):v))return!1}}return!0},Xn=function(t){for(var r=Kn(t),e=r.length;e--;){var n=r[e],o=t[n];r[e]=[n,o,Hn(o)]}return r},Yn=Zn;var to=function(t,r){return null!=t&&r in Object(t)},ro=Ar;var eo=Gn,no=ir,oo=function(t,r){return null!=t&&ro(t,r,to)},io=S,uo=Vn,ao=Zn,co=tr;var fo=function(t){return function(r){return null==r?void 0:r[t]}},so=nr;var lo=fo,vo=function(t){return function(r){return so(r,t)}},po=S,ho=tr;var bo=function(t){var r=Xn(t);return 1==r.length&&r[0][2]?Yn(r[0][0],r[0][1]):function(e){return e===t||Qn(e,t,r)}},yo=function(t,r){return io(t)&&uo(r)?ao(co(t),r):function(e){var n=no(e,t);return void 0===n&&n===r?oo(e,t):eo(r,n,3)}},_o=_e,go=e,jo=function(t){return po(t)?lo(ho(t)):vo(t)};var wo=function(t){return"function"==typeof t?t:null==t?_o:"object"==typeof t?go(t)?yo(t[0],t[1]):bo(t):jo(t)},mo=wo,Oo=fe,So=pe;var zo=function(t,r,e,n){for(var o=t.length,i=e+(n?1:-1);n?i--:++i<o;)if(r(t[i],i,t))return i;return-1},Ao=z,xo=g,No=/^\s+|\s+$/g,Io=/^[-+]0x[0-9a-f]+$/i,Bo=/^0b[01]+$/i,Eo=/^0o[0-7]+$/i,ko=parseInt;var Ro=function(t){if("number"==typeof t)return t;if(xo(t))return NaN;if(Ao(t)){var r="function"==typeof t.valueOf?t.valueOf():t;t=Ao(r)?r+"":r}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(No,"");var e=Bo.test(t);return e||Eo.test(t)?ko(t.slice(2),e?2:8):Io.test(t)?NaN:+t},Mo=1/0;var Po=function(t){return t?(t=Ro(t))===Mo||t===-1/0?17976931348623157e292*(0>t?-1:1):t==t?t:0:0===t?t:0};var To=function(t){var r=Po(t),e=r%1;return r==r?e?r-e:r:0},Co=zo,$o=wo,qo=To,Do=Math.max;var Fo=function(t){return function(r,e,n){var o=Object(r);if(!Oo(r)){var i=mo(e);r=So(r),e=function(t){return i(o[t],t,o)}}var u=t(r,e,n);return u>-1?o[i?r[u]:u]:void 0}}((function(t,r,e){var n=null==t?0:t.length;if(!n)return-1;var o=null==e?0:qo(e);return 0>o&&(o=Do(n+o,0)),Co(t,$o(r),o)})),Uo=d,Go=e,Jo=b;var Wo=function(t){return"string"==typeof t||!Go(t)&&Jo(t)&&"[object String]"==Uo(t)},Lo=fo("length"),Vo=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");var Ho=function(t){return Vo.test(t)},Ko="\\ud800-\\udfff",Zo="["+Ko+"]",Qo="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Xo="\\ud83c[\\udffb-\\udfff]",Yo="[^"+Ko+"]",ti="(?:\\ud83c[\\udde6-\\uddff]){2}",ri="[\\ud800-\\udbff][\\udc00-\\udfff]",ei="(?:"+Qo+"|"+Xo+")"+"?",ni="[\\ufe0e\\ufe0f]?",oi=ni+ei+("(?:\\u200d(?:"+[Yo,ti,ri].join("|")+")"+ni+ei+")*"),ii=RegExp(Xo+"(?="+Xo+")|"+("(?:"+[Yo+Qo+"?",Qo,ti,ri,Zo].join("|")+")")+oi,"g");var ui=Lo,ai=Ho,ci=function(t){for(var r=ii.lastIndex=0;ii.test(t);)++r;return r};var fi=ue,si=Nn,li=fe,vi=Wo,pi=function(t){return ai(t)?ci(t):ui(t)};var hi=function(t){if(null==t)return 0;if(li(t))return vi(t)?pi(t):t.length;var r=si(t);return"[object Map]"==r||"[object Set]"==r?t.size:fi(t).length},di=Vt;var bi=function(t){return di(t).toLowerCase()};var yi=zo,_i=function(t){return t!=t},gi=function(t,r,e){for(var n=e-1,o=t.length;++n<o;)if(t[n]===r)return n;return-1};var ji=qt;var wi=function(t,r){return ji(r,(function(r){return t[r]}))},mi=pe;var Oi=function(t,r,e){return r==r?gi(t,r,e):yi(t,_i,e)},Si=fe,zi=Wo,Ai=To,xi=function(t){return null==t?[]:wi(t,mi(t))},Ni=Math.max;var Ii=function(t,r,e,n){t=Si(t)?t:xi(t),e=e&&!n?Ai(e):0;var o=t.length;return 0>e&&(e=Ni(o+e,0)),zi(t)?o>=e&&t.indexOf(r,e)>-1:!!o&&Oi(t,r,e)>-1};function Bi(){return!("undefined"==typeof window)}var Ei="",ki=null;function Ri(){return Bi()&&window.__DEV__?bi(navigator.userAgent):Ei}function Mi(){return Ii(Ri(),"android")}function Pi(){return/iphone|ipad|ipod/i.test(Ri())}function Ti(){return/finance/i.test(Ri())}function Ci(){return/FinshellWebSDK/i.test(Ri())}function $i(){return(/(JSBridge.*wallet|wallet.*JSBridge)/i.test(Ri())||Ci()&&/JSBridge\/\w+ tas/i.test(Ri()))&&!/hap/i.test(Ri())}function qi(){return/fintech_IN/i.test(Ri())}function Di(t,r){void 0===t&&(t=Ri()),void 0===r&&(r=!0);var e=t.match(/FinshellWebSDK\/([0-9.]+)/i);return e&&e[1]?r?Number(e[1].split(".").map((function(t){return 1===t.length?"0"+t:t})).join("")):e[1]:0}function Fi(t){if(void 0===t&&(t=Ri()),Ti())return function(t){void 0===t&&(t=Ri());var r=t.match(/finance\/([0-9.]+)/i);if(r&&r[1]){var e=Number(r[1].split(".").map((function(t){return 1===t.length?"0"+t:t})).join(""));return 11e3>e?Number(r[1].split(".").join("")):e}return 0}(t);if(qi())return function(t){void 0===t&&(t=Ri());var r=t.match(/fintech_IN\/([0-9.]+)/i);return r&&r[1]?Number(r[1].split(".").map((function(t){return 1===t.length?"0"+t:t})).join("")):0}();var r=t&&t.match(/wallet\/([0-9.]+)/i)||t&&t.match(/tas\/([0-9.]+)/i);if(r&&r[1]){var e=Number(r[1].split(".").map((function(t){return 1===t.length?"0"+t:t})).join(""));return Number(31e3>e?r[1].split(".").join(""):e)}return 0}function Ui(){if(3e6>Di()&&!qi())return!1;if(null!==ki)return ki;var t=Fi();if(Ti()&&t>=191)ki=!0;else if($i()&&t>=360)if(360===t){var r=function(){var t=(Ri().match(/wallet\/(\w\.?)+/g)||[])[0],r=Number((void 0===t?"":t).substr(-6));return isNaN(r)?0:r}();ki=!isNaN(r)&&r>=200915}else ki=2100>t||t>=3e3;else ki=!(!qi()||20841>t);return ki}Bi()&&(Ei=bi(navigator.userAgent));var Gi,Ji="RainbowBridge",Wi=function(){function t(t){this.bizId=Ji,this.reqMethods={},this._isBrowser=Bi(),t&&this.isSupportBizId()&&(this.bizId=t),this._isBrowser&&(this.increment=function(){window.O_BRIDGE_COUNT?window.O_BRIDGE_COUNT++:window.O_BRIDGE_COUNT=1;var t=window.O_BRIDGE_COUNT+"0000000";return parseInt(t)}())}return t.prototype.init=function(){if(this._isBrowser&&window.parent!==window){var t=((new Date).getTime()+"").substr(-6)+"000";"0"===t[0]&&(t="5"+t.substring(1,t.length)),this.increment=Number(t)}},t.prototype.isSupportBizId=function(){return function(t){var r=t.wallet,e=void 0===r?0:r,n=t.ios,o=void 0===n?0:n,i=t.android,u=void 0===i?0:i,a=t.websdk,c=void 0===a?0:a,f=t.fineasy,s=void 0===f?0:f,l=Fi();if(Ti()){if(Pi()&&o)return l>=o;if(Mi()&&u)return l>=u}else{if(Ci()&&c)return Di()>=c;if($i()&&e)return l>=e;if(qi()&&s)return l>=s}return!1}({fineasy:21051})},t.prototype._invoke=function(t,r,e,n,o){var i=void 0===o?{}:o,u=i.throttle,a=void 0===u||u,c=i.loop,f=void 0!==c&&c;if(n&&!I(n))throw Error("callback is not a function");var s=r+"_"+JSON.stringify(e||{});a||(s+="_no_throttle_"+this.privateGeneratePort()),I(n)&&this.register(s,n,{loop:f}),I(n)&&1!==hi(ir(this,["reqMethods",s,"callbacks"]))||this.exec(t,this.getPort(s),r,e)},t.prototype.register=function(t,r,e){var n=e.loop,o=void 0!==n&&n;Ir(this.reqMethods,t)?this.reqMethods[t].callbacks.push(r):this.reqMethods[t]={port:this.privateGeneratePort(),callbacks:[r],loop:o}},t.prototype.unRegister=function(t){var r=this;Se(this.reqMethods,(function(e,n){if(e.port===Number(t))return e.loop||delete r.reqMethods[n],!1}))},t.prototype.getPort=function(t){return ir(this.reqMethods,[t,"port"])||this.privateGeneratePort()},t.prototype.privateGeneratePort=function(){return this.increment++},t.prototype.getReqMethod=function(t){return Fo(this.reqMethods,["port",t])},t.prototype.onComplete=function(t,r){if(this.increment<t)for(var e=Array.prototype.slice.call(window.document.querySelectorAll("iframe")),n=0;e.length>n;n++)ir(e[n],"contentWindow."+this.bizId+".onComplete")&&e[n].contentWindow[this.bizId].onComplete(t,r);else{var o=function(t){if(!t||"string"!=typeof t)return t||{};try{return JSON.parse(t)}catch(t){return{status:{code:1,msg:"params parse error!"}}}}(r),i=this.getReqMethod(t);i&&this.doResponse(i,o)}},t.prototype.onCancel=function(t){var r=this.getReqMethod(t);if(r){this.doResponse(r,{status:{code:0,msg:""},data:{cancel:!0}})}},t.prototype.doResponse=function(t,r){try{Se(t.callbacks,(function(t){t(r)}))}catch(t){console.warn("callback exec error",t)}finally{this.unRegister(t.port)}},t.prototype.isNoExec=function(){return!($i()||Ti()||/usercenter/i.test(Ri())||/fintech_ID/i.test(Ri())||qi()||Ci())},t.prototype.exec=function(t,r,e,n){if(!this.isNoExec()){var o=window===window.parent?window:window.parent,i=Mi()&&ir(o,"RainbowBridgeEx.rainbowCallNative",null),u=Pi()&&ir(o,"webkit.messageHandlers.RainbowBridge",null),a=function(t){return t&&"object"==typeof t?JSON.stringify(t):t||"{}"}(n);"ajaxAgent"===e&&Mi()&&(a=encodeURIComponent(a));var c="rainbow://"+t+":"+r+"/"+e+(this.bizId===Ji?"":"/"+this.bizId)+"?"+a;this._isBrowser&&(i&&(Ui()||Ci())?o.RainbowBridgeEx.rainbowCallNative(c):u?o.webkit.messageHandlers.RainbowBridge.postMessage({uri:c}):o.prompt(c,""))}},t}();!function(t){t[t.Success=0]="Success"}(Gi||(Gi={}));var Li=/^[a-zA-Z_$][a-zA-Z0-9_$]*$/;return function(r){function e(t){var e=this;if(!function(t){return!!Li.test(t)}(t))throw Error("bizId is required");return(e=r.call(this,t="O_Fin_"+t)||this).mount(t),e.init(),e.invoke({method:"batchStatistics",params:{events:[{e:"102",ei:"outer_jsapi_init",d:JSON.stringify({bizId:t,url:location.href,ua:navigator.userAgent,referrer:document.referrer})}],options:{server:1,priority:1e3}}}),e}return function(r,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+e+" is not a constructor or null");function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}(e,r),e.prototype.mount=function(t){Bi()&&(this.isSupportBizId()?window[t]=this:window.RainbowBridge=this)},e.prototype.invoke=function(t){var r=t.type,e=void 0===r?"JSInterfaceMethod":r,n=t.method,o=t.params,i=void 0===o?{}:o,u=t.onSuccess,a=t.onFail;this._invoke(e,n,i,u||a?function(t){ir(t,"status.code")!==Gi.Success?a&&a(t):u&&u(t.data)}:null)},e}(Wi)}));
