import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { InToast } from '../components/InToast'
import { InLoading } from '../components/InLoading'
import { browser } from '../utils'

export interface IRequest extends AxiosRequestConfig {
  hideLoading?: boolean
  hideToast?: boolean
  handleErrorSelf?: boolean
}

export interface IResponse<T> {
  result: number
  resultMessage: string
  content?: T
}

export class Request {
  private axiosInstance: AxiosInstance
  private loadingCount = 0
  constructor(
    private defaultRequestConfig: IRequest,
    protected requestInterCeptors?: (res: InternalAxiosRequestConfig) => void,
    protected reponseInterCeptors?: (res: AxiosResponse) => any,
    protected responseErrorHandler?: (err: any) => any
  ) {
    this.axiosInstance = axios.create(defaultRequestConfig)
    this.axiosInstance.interceptors.request.use(
      (res: InternalAxiosRequestConfig) => {
        console.log('api in: ', res)
        if (this.requestInterCeptors) {
          this.requestInterCeptors(res)
        }
        if ((res as IRequest).hideLoading === false) {
          this.showLoading()
        }
        return res
      },
      (error) => {
        return Promise.reject(error)
      }
    )
    this.axiosInstance.interceptors.response.use(
      (res: AxiosResponse) => {
        console.log('api out: ', res)
        const config = res.config as IRequest
        //loading
        if (config.hideLoading === false) {
          this.closeLoading()
        }
        //toast
        if (config.hideToast === false && res.data.result !== 0) {
          InToast.info(res.data.resultMessage)
        }
        if (this.reponseInterCeptors) {
          return this.reponseInterCeptors(res)
        }
        if (res.data.result === 0 || config.handleErrorSelf === true) {
          return res.data
        } else {
          return Promise.reject(res.data)
        }
      },
      (error) => {
        //loading
        if (error.config.hideLoading === false) {
          this.closeLoading()
        }
        //toast
        if (error.config.hideToast === false && error.message !== 'canceled') {
          InToast.info(error.message)
        }
        if (this.responseErrorHandler && this.responseErrorHandler(error)) {
          return this.responseErrorHandler(error)
        }
        return Promise.reject(error)
      }
    )
  }
  private showLoading() {
    this.loadingCount++
    InLoading.show()
  }
  private closeLoading() {
    this.loadingCount--
    if (this.loadingCount <= 0) {
      InLoading.close()
      this.loadingCount = 0
    }
  }
  private request<T>(request: IRequest): Promise<IResponse<T>> {
    return new Promise<IResponse<T>>((resolve, reject) => {
      this.axiosInstance
        .request({ ...request, headers: { ...this.defaultRequestConfig.headers, ...request.headers } })
        .then((res) => {
          resolve(res as unknown as IResponse<T>)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  post<T>(request: IRequest) {
    const postRequest = Object.assign(request, { method: 'post' })
    return this.request<T>(postRequest)
  }

  get<T>(request: IRequest) {
    const postRequest = Object.assign(request, { method: 'get' })
    return this.request<T>(postRequest)
  }
}
