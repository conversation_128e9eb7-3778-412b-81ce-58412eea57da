import { Ref, isRef, ref, set } from 'vue';

export function useModelInput<T extends Object>(model: T | Ref<T>) {
  return (key: keyof T) => (val: T[keyof T]) => {
    if (isRef(model)) {
      (model as Ref).value[key] = val;
    } else {
      if (Object.prototype.hasOwnProperty.call(model, key)) {
        (model as T)[key] = val;
      } else {
        set(model, key as string, val);
      }
    }
  };
}

export const useInDialog = () => {
  const dialog = ref();
  // 以下方法是必需的
  // (不要改变它的名称 --> "show")
  const show = () => {
    dialog.value.show();
  };
  // 以下方法是必需的
  // (不要改变它的名称 --> "hide")
  const hide = () => {
    dialog.value.hide();
  };

  return {
    dialog,
    show,
    hide
  };
};
