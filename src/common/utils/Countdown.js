function checkTime(time) {
  if (time <= 0) {
    return '00'
  }
  let t = time
  if (t < 10) {
    t = '0' + t
  }
  return t.toString()
}
export class Countdown {
  result = {
    dd: 0,
    hh: 0,
    mm: 0,
    ss: 0
  }

  /**
   * 开始时间
   *
   * @memberof Countdown
   */
  startTime = Date

  /**
   * 结束时间
   *
   * @memberof Countdown
   */
  endTime = Date

  /**
   * 时间差
   *
   * @memberof Countdown
   */
  diff = 0

  callback // 第一个参数：是否结束， 第二个参数：当前倒计时时间

  timer

  constructor(startTime, endTime, callback) {
    if (typeof startTime === 'number') {
      this.startTime = new Date()
      this.endTime = new Date(this.startTime.getTime() + startTime)
      this.callback = endTime
      this.result.ss = parseInt((startTime / 1000).toString(), 10)
    } else {
      this.startTime = startTime
      this.endTime = endTime
      this.callback = callback
    }
    this.diff = this.endTime.getTime() - this.startTime.getTime()
    this.setValue()
    this.callback(false, this.result)
    this.run()
  }

  setValue() {
    this.result.dd = checkTime(parseInt((this.diff / 1000 / 60 / 60 / 24).toString(), 10))
    this.result.hh = checkTime(parseInt(((this.diff / 1000 / 60 / 60) % 24).toString(), 10))
    this.result.mm = checkTime(parseInt(((this.diff / 1000 / 60) % 60).toString(), 10))
    this.result.ss = checkTime(Math.round(((this.diff / 1000) % 60).toString(), 10))
  }

  async run() {
    const beforeDate = new Date()
    this.timer = setTimeout(() => {
      const afterDate = new Date()
      this.diff -= afterDate.getTime() - beforeDate.getTime() // 获取真实1s的时间
      this.setValue()

      if (this.diff <= 300) {
        if (this.callback) {
          this.callback(true, this.result)
        }
      } else {
        this.run()
        if (this.callback) {
          this.callback(false, this.result)
        }
      }
    }, 1000)
  }

  clear() {
    clearTimeout(this.timer)
  }
}
