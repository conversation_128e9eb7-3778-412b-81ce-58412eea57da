import AES from 'crypto-js/aes'
import encUtf8 from 'crypto-js/enc-utf8'
import encHex from 'crypto-js/enc-hex'
import encBase64 from 'crypto-js/enc-base64'
import modeEcb from 'crypto-js/mode-ecb'
import padPkcs7 from 'crypto-js/pad-pkcs7'

export const AesEncrypt = (str, key) => {
  if (Object.prototype.toString.call(str) === '[object Object]') {
    str = JSON.stringify(str)
  }
  key = encUtf8.parse(key)
  let encryptedData = AES.encrypt(str, key, {
    mode: modeEcb,
    padding: padPkcs7
  })
  return encryptedData.toString()
}

export const AesDecrypt = (str, key) => {
  key = encUtf8.parse(key)
  let encryptedHexStr = encHex.parse(str)
  let encryptedBase64Str = encBase64.stringify(encryptedHexStr)

  let decryptedData = AES.decrypt(encryptedBase64Str, key, {
    mode: modeEcb,
    padding: padPkcs7
  })

  let decryptedStr = decryptedData.toString(encUtf8)

  return decryptedStr
}
