import qs from 'qs'
import { browser } from './browser'
import { versionCopamre, getAppVersion } from './app'
/**
 * @description: 判断是否字符串
 * @param {*}
 * @return {*}
 */
export const isString = (target) => {
  return Object.prototype.toString.call(target) === '[object String]'
}

/**
 * @description: 生成随机字符串
 * @param {*}
 * @return {*}
 */
export const randomString = (
  len = 32,
  charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890'
) => {
  var maxPos = charSet.length
  var ret = ''
  for (let i = 0; i < len; i++) {
    ret += charSet.charAt(Math.floor(Math.random() * maxPos))
  }
  return ret
}

/**
 * @description: 下载外部js
 * @param {*}
 * @return {*}
 */
export const loadScriptString = (src) => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script') // 创建一个script标签
    script.type = 'text/javascript'
    // IE浏览器认为script是特殊元素,不能再访问子节点;报错;
    script.src = src
    script.onload = () => {
      resolve()
    }
    script.onerror = () => {
      reject()
    }
    document.head.appendChild(script)
  })
}

/**
 * @description: 获取url参数
 * @param {*} variable
 * @return {*}
 */
export const getQueryString = (variable) => {
  const query = window.location.search.substring(1)
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (pair[0] == variable) {
      return pair[1]
    }
  }
  return null
}

/**
 * @description: 复制
 * @param {*} text
 * @return {*}
 */
export const copy = (text) => {
  const textarea = document.createElement('textarea')
  // Fix bug of unexpectable page scroll to bottom on iOS 12 (maybe below)
  textarea.style.opacity = '0'
  document.body.appendChild(textarea)
  textarea.value = text
  textarea.select()
  if (browser.iOS) {
    // Fix bug of copy faild on iOS 12 (maybe below). Test on each verison of iOS, it work.
    textarea.setSelectionRange(0, textarea.value.length)
  }

  document.execCommand('copy')
  textarea.remove()
}

/**
 * @description: 字符串插入分隔符
 * @param {string} val
 * @param {number} num
 * @param {string} symbol
 * @return {*}
 */
export function joinSymbol(val, num = 4, symbol = ' ') {
  const strToArr = val.split('')
  for (var i = num; i < val.length; i += num + 1) {
    strToArr.splice(i, 0, symbol)
  }
  return strToArr.join('')
}

/**
 * @description: url追加参数
 * @param url
 * @param param
 * @return {*}
 */
export function appendUrlParam(url, param = {}) {
  const paramString = qs.stringify(param)
  if (paramString === '') return url
  const hadParam = url.indexOf('?') > -1
  if (hadParam) {
    return `${url}&${qs.stringify(param)}`
  } else {
    return `${url}?${qs.stringify(param)}`
  }
}

/**
 * @description: 安卓4.2.0及以前bridge的ajax方法返回值会decode，所以这边原本的%2B会变成 +，此处需要还原回去
 * @param {*} docUrl
 * @return {*}
 */
export function fixDocUrl(docUrl) {
  // if (browser.app && browser.android && versionCopamre(getAppVersion(), '4.2.0') <= 0) {
  if (browser.app && browser.android) {
    return docUrl?.replaceAll('+', '%2B')
  }
  return docUrl
}

/**
 * @description: set ios full screen
 * @return {*}
 */
export function setImmersiveScreen() {
  document.querySelector(`meta[name='viewport']`)['content'] += ', viewport-fit=cover'
}

/**
 * 将(x,y)中的v值，映射到(a,b)中
 * a + (b - a) * ((v - x) / (y - x))
 * @param {*} value
 * @param {*} valueRangeStart
 * @param {*} valueRangeEnd
 * @param {*} newRangeStart
 * @param {*} newRangeEnd
 * @returns
 */
export function rangeMap(value, valueRangeStart, valueRangeEnd, newRangeStart, newRangeEnd) {
  return (
    newRangeStart +
    (newRangeEnd - newRangeStart) * ((value - valueRangeStart) / (valueRangeEnd - valueRangeStart))
  )
}

export const sleep = (timeout) => {
  return new Promise((r) => {
    setTimeout(() => {
      r()
    }, timeout)
  })
}

const isIp = (str) => {
  const reg =
    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
  return reg.test(str)
}

export const getCurrentEnv = () => {
  const hostName = location.hostname
  if (hostName === 'lite.cashcerdas.id') {
    return 'pro'
  }
  if (hostName === 'uat-lite.cashcerdas.id') {
    // 预发同生产
    return 'pro'
  }
  if (hostName === 'fat-lite.cashcerdas.id') {
    return 'test'
  }
  if (hostName === 'localhost' || isIp(hostName)) {
    return 'dev'
  }
  return 'pro'
}

export const isCurrentEnv = () => {
  const currentEnv = getCurrentEnv()
  return {
    PRO: currentEnv === 'pro',
    TEST: currentEnv === 'test',
    DEV: currentEnv === 'dev'
  }
}

export function uuid() {
  var raw = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })

  return raw.replaceAll('-', '') // i don't like '-'
}
