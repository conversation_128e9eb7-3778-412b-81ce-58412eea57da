/**
 * 获取App版本号
 * @returns {String}
 */
export const getAppVersion = () => {
  const uaMatch = navigator.userAgent.match(/WebView_(\d+\.?)+/)
  const appVersion = uaMatch && uaMatch[0].replace('WebView_', '')
  console.log(appVersion)

  return appVersion || ''
}

/**
 * @description: 版本号对比，a>b返回1  a=b返回0  a<b返回-1
 * @param {*}
 * @return {*}
 */
export const versionCopamre = (versionA, versionB) => {
  let splitedA = versionA.split('.')
  let splitedB = versionB.split('.')

  for (let i = 0; i < Math.max(splitedA.length, splitedB.length); i++) {
    if (splitedA.length === i) {
      let partB = splitedB[i]
      return partB == 0 ? 0 : -1
    }
    if (splitedB.length === i) {
      let partA = splitedA[i]
      return partA == 0 ? 0 : 1
    }
    let partA = splitedA[i]
    let partB = splitedB[i]
    if (partA > partB) {
      return 1
    } else if (partA < partB) {
      return -1
    }
  }

  return 0
}

/**
 * @description: 当前 APP 版本是否大于等于指定版本
 * @param version {*} 指定版本
 * @return {*}
 */
export const versionAvailable = (version = '1.0.0') => {
  return versionCopamre(getAppVersion(), version) >= 0
}
