import { testPattern } from '@/common/utils/patterns';

const INPUT_PLS = 'Silakan masukkan'
const SELECT_PLS = 'Silakan pilih'
const CAN_NOT_BE_EMPYT = 'Tidak boleh kosong'

export class FormatChecker {
  MAX_LENGTH = 50;

  constructor() {}

  getTrimedText(text: string) {
    return text?.toString().trim() ?? text;
  }

  getEmptyWarn() {
    return CAN_NOT_BE_EMPYT;
  }

  checkRequired(text: string) {
    const _text = this.getTrimedText(text);
    if (_text === void 0 || _text === null || _text === '') {
      return this.getEmptyWarn()
    } else {
      return true;
    }
  }

  check(text: string): string | boolean {
    return this.checkRequired(text);
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

export class FormatCheckerName extends FormatChecker {
  MAX_LENGTH = 50;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return (
      testPattern.isName(_text) ||
      'Nama harus alphabet, contoh Sercy Sabrin, silakan masukkan konten yang benar.'
    );
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

export class FormatCheckerMobile extends FormatChecker {
  MAX_LENGTH = 20;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return (
      testPattern.isMobile(_text) || 'Harap isi 10-13 digit nomor ponsel yang benar diawali dengan angka 08'
    );
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }

  getEmptyWarn() {
    return INPUT_PLS;
  }
}

export class FormatCheckerEducation extends FormatChecker {
  constructor() {
    super();
  }

  getEmptyWarn() {
    return SELECT_PLS;
  }
}

export class FormatCheckerFundsSource extends FormatChecker {
  constructor() {
    super();
  }

  getEmptyWarn() {
    return SELECT_PLS;
  }
}

export class FormatCheckerEmail extends FormatChecker {
  MAX_LENGTH = 60;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return testPattern.isEmail(_text) || 'Format alamat email Anda salah, mohon isi alamat email yang benar.';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }

  getEmptyWarn() {
    return INPUT_PLS;
  }
}

/**
 * @description: 婚姻状况
 * @return {*}
 */
export class FormatCheckerWedding extends FormatChecker {
  constructor() {
    super();
  }
}

/**
 * @description: 母亲的姓氏
 * @return {*}
 */
export class FormatCheckerLastNameOfMother extends FormatChecker {
  MAX_LENGTH = 50;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return testPattern.isName(_text) || 'Harap masukkan nama belakang yang terdiri dari 2-50 karakter';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

/**
 * @description: 贷款原因
 * @return {*}
 */
export class FormatCheckerBorrowTarget extends FormatChecker {
  constructor() {
    super();
  }
}

/**
 * @description: 居住状态
 * @return {*}
 */
export class FormatCheckerLivingState extends FormatChecker {
  constructor() {
    super();
  }
}

/**
 * @description: 住址
 * @return {*}
 */
export class FormatCheckerLivingAddress extends FormatChecker {
  MAX_LENGTH = 200;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return testPattern.isValidChar(_text) || 'Format alamat tidak benar, silakan masukkan konten yang benar';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

/**
 * @description: 工作类型
 * @return {*}
 */
export class FormatCheckerJobType extends FormatChecker {
  constructor() {
    super();
  }

  getEmptyWarn() {
    return SELECT_PLS;
  }
}

/**
 * @description: 公司名称
 * @return {*}
 */
export class FormatCheckerCompanyName extends FormatChecker {
  MAX_LENGTH = 100;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return (
      /^[ -~]+$/.test(_text) || 'Format nama perusahaan tidak benar, silakan masukkan konten yang benar.'
    );
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

/**
 * @description: 月收入
 * @return {*}
 */
export class FormatCheckerMonthlyIncome extends FormatChecker {
  MAX_LENGTH = 9;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return (
      (/^[1-9]\d{0,8}$/.test(_text) && !_text.startsWith('0')) ||
      'Format pada pendapatan per bulan salah, silakan masukkan konten yang benar.'
    );
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }

  getEmptyWarn() {
    return INPUT_PLS;
  }
}

/**
 * @description: 工作年限
 * @return {*}
 */
export class FormatCheckerWorkAge extends FormatChecker {
  constructor() {
    super();
  }

  getEmptyWarn() {
    return SELECT_PLS;
  }
}

/**
 * @description: 联系人关系
 * @return {*}
 */
export class FormatCheckerContactRelation extends FormatChecker {
  constructor() {
    super();
  }
}

/**
 * @description: NPWP
 * @return {*}
 */
export class FormatCheckerNPWP extends FormatChecker {
  MAX_LENGTH = 15;

  constructor() {
    super();
  }

  check(text: string) {
    if (!text) return true;
    const _text = this.getTrimedText(text);
    return /^\d{15}$/.test(_text) || 'NPWP harus 15 angka.';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

/**
 * @description: Ktp姓名
 * @return {*}
 */
export class FormatCheckerKtpName extends FormatChecker {
  MAX_LENGTH = 50;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return testPattern.isName(_text) || 'Format tidak benar, harus diantara 2-50 karakter.';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }

  getEmptyWarn() {
    return INPUT_PLS;
  }
}

/**
 * @description: Ktp号码
 * @return {*}
 */
export class FormatCheckerKtpNumber extends FormatChecker {
  MAX_LENGTH = 20;

  constructor() {
    super();
  }

  isAllSameChar(str: string) {
    if (!str) return false;
    for (let i = 0; i < str.length; i++) {
      for (let j = i + 1; j < str.length; j++) {
        if (str[i] !== str[j]) {
          return false;
        }
      }
    }
    return true;
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    return (
      (/^\d{16}$/.test(_text) && !this.isAllSameChar(_text)) ||
      'Format tidak benar, silakan masukkan kembali.'
    );
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }

  getEmptyWarn() {
    return INPUT_PLS;
  }
}

/**
 * @description: 商户名称
 * @return {*}
 */
export class FormatCheckerMerchantName extends FormatChecker {
  MAX_LENGTH = 100;

  constructor() {
    super();
  }

  check(text: string) {
    const _text = this.getTrimedText(text);
    if (_text.length < 2) return 'Informasi Pedagang format salah, harap masukkan kembali';
    return /^[ -~]+$/.test(_text) || 'Informasi Pedagang format salah, harap masukkan kembali';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

/**
 * @description: NIB
 * @return {*}
 */
export class FormatCheckerNIB extends FormatChecker {
  MAX_LENGTH = 50;

  constructor() {
    super();
  }

  check(text: string) {
    if (!text) return true;
    const _text = this.getTrimedText(text);
    return /^\d{1,50}$/.test(_text) || 'Nomor registrasi bisnis format salah, harap masukkan kembali';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}

/**
 * @description: 银行卡号
 * @return {*}
 */
export class FormatCheckerBankNumber extends FormatChecker {
  MAX_LENGTH = 16;

  constructor() {
    super();
  }

  check(text: string) {
    if (!text) return true;
    const _text = this.getTrimedText(text);
    return /^\d{8,16}$/.test(_text) || 'Nomor Kartu format salah, harap masukkan kembali';
  }

  get formValidator() {
    return {
      rules: [(text: string) => this.checkRequired(text), (text: string) => this.check(text)],
      maxLength: this.MAX_LENGTH
    };
  }
}
export const formatChecker = {
  Base: new FormatChecker(),
  Name: new FormatCheckerName(),
  Mobile: new FormatCheckerMobile(),
  Education: new FormatCheckerEducation(),
  FundsSource: new FormatCheckerFundsSource(),
  Email: new FormatCheckerEmail(),
  Wedding: new FormatCheckerWedding(),
  LastNameOfMother: new FormatCheckerLastNameOfMother(),
  BorrowTarget: new FormatCheckerBorrowTarget(),
  LivingState: new FormatCheckerLivingState(),
  LivingAddress: new FormatCheckerLivingAddress(),
  JobType: new FormatCheckerJobType(),
  CompanyName: new FormatCheckerCompanyName(),
  MonthlyIncome: new FormatCheckerMonthlyIncome(),
  WorkAge: new FormatCheckerWorkAge(),
  ContactRelation: new FormatCheckerContactRelation(),
  NPWP: new FormatCheckerNPWP(),
  KtpName: new FormatCheckerKtpName(),
  KtpNumber: new FormatCheckerKtpNumber(),
  MerchantName: new FormatCheckerMerchantName(),
  NIB: new FormatCheckerNIB(),
  BankNumber: new FormatCheckerBankNumber()
};
