function blobToFile(blob) {
  return new File([blob], 'img.jpeg', {
    type: 'image/jpeg'
  })
}

function imageURLResizeAndCompress(imageURL, quality, maxPixel, returnFile, callback) {
  let image = new Image()
  image.onload = () => {
    let canvas = document.createElement('canvas')
    let context = canvas.getContext('2d')
    if (image.width > image.height) {
      if (image.width > maxPixel) {
        canvas.width = maxPixel
        canvas.height = (maxPixel / image.width) * image.height
      } else {
        canvas.width = image.width
        canvas.height = image.height
      }
    } else {
      if (image.height > maxPixel) {
        canvas.height = maxPixel
        canvas.width = (maxPixel / image.height) * image.width
      } else {
        canvas.width = image.width
        canvas.height = image.height
      }
    }

    context.drawImage(image, 0, 0, canvas.width, canvas.height)
    if (returnFile) {
      canvas.toBlob(
        (blob) => {
          const file = blobToFile(blob)
          callback(file)
        },
        'image/jpeg',
        quality
      )
    } else {
      callback(canvas.toDataURL('image/jpeg', quality))
    }
  }
  image.src = imageURL
}

/**
 * fileResizeAndCompress 对传入的Image进行压缩
 * @param returnDataType 返回的数据类型 true=blob false=base64字符串
 */
export const imageResizeAndCompress = (imageFile, quality, maxPixel, returnFile, callback) => {
  let fileReader = new FileReader()
  fileReader.onload = (event) => {
    imageURLResizeAndCompress(event.target.result, quality, maxPixel, returnFile, callback)
  }
  fileReader.readAsDataURL(imageFile)
}

export const stringToUnit8Array = (str) => {
  let n = str.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = str.charCodeAt(n)
  }
  return u8arr
}

export const base64ToUnit8Array = (dataurl) => {
  let arr = dataurl.split(','),
    bstr = atob(arr[1])

  return stringToUnit8Array(bstr)
}

/**
 * 将base64编码的图片转换成File对象
 */
export const imageBase64toFile = (dataurl, filename) => {
  //将base64转换为文件
  let arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1]

  const u8arr = base64ToUnit8Array(dataurl)
  const blob = new Blob([u8arr], { type: mime })
  blob.name = filename
  blob.lastModifiedDate = new Date()
  return blob
}
