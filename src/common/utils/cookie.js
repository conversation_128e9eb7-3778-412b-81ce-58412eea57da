import Cookies from 'js-cookie'
import { isString } from './helper'

export const cookie = {
  set(key, value, option) {
    if (isString(value)) {
      Cookies.set(key, value, option)
    } else {
      Cookies.set(key, JSON.stringify(value), option)
    }
  },

  get(key) {
    let v = Cookies.get(key)
    try {
      return JSON.parse(v)
    } catch (error) {
      return v
    }
  },

  remove(key, option) {
    Cookies.remove(key, option)
  }
}
