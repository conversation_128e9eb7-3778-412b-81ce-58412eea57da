/**
 * LocalStorage
 *
 */
export const localStorage = {
  /**
   * 存储localStorage
   * @param {*} name 键
   * @param {*} content 值
   */
  set(key, _value) {
    if (!key) return
    let value = _value
    if (typeof value !== 'string') {
      value = JSON.stringify(value)
    }
    window.localStorage.setItem(key, value)
  },

  /**
   * 获取localStorage
   * @param {*} name 键
   */
  get(key) {
    if (!key) return
    let value = window.localStorage.getItem(key)
    console.log('get', value)
    try {
      const objectValue = JSON.parse(value)
      value = objectValue instanceof Object ? objectValue : value
    } catch (error) {}
    return value
  },

  /**
   * 删除localStorage
   * @param {*} name 键
   */
  remove(key) {
    if (!key) return
    window.localStorage.removeItem(key)
  }
}

/**
 * sessionStorage
 *
 */
export const sessionStorage = {
  /**
   * 存储localStorage
   * @param {*} name 键
   * @param {*} content 值
   */
  set(key, _value) {
    if (!key) return
    let value = _value
    if (typeof value !== 'string') {
      value = JSON.stringify(value)
    }
    window.sessionStorage.setItem(key, value)
  },

  /**
   * 获取sessionStorage
   * @param {*} name 键
   */
  get(key) {
    if (!key) return
    let value = window.sessionStorage.getItem(key)
    try {
      const objectValue = JSON.parse(value)
      value = objectValue instanceof Object ? objectValue : value
    } catch (error) {}
    return value
  },

  /**
   * 删除sessionStorage
   * @param {*} name 键
   */
  remove(key) {
    if (!key) return
    window.sessionStorage.removeItem(key)
  }
}
