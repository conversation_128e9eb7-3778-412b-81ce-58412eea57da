import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
import timezone from 'dayjs/plugin/timezone'
dayjs.extend(timezone)

export const formatTimeToDesc = (timestamp, format = 'DD/MM/YYYY') => {
  // const now = dayjs(dayjs().tz('Asia/Jakarta').format('YYYY-MM-DD')).tz('Asia/Jakarta')
  // const d = dayjs(dayjs(Number(timestamp)).tz('Asia/Jakarta').format('YYYY-MM-DD')).tz('Asia/Jakarta')
  // const dayDiff = now.diff(d, 'day')
  // if (dayDiff === 0) {
  //   return 'Hari Ini'
  // } else if (dayDiff === 1) {
  //   return 'Kemarin'
  // } else if (dayDiff === 2) {
  //   return 'Kemarin lusa'
  // } else {
  return dayjs(Number(timestamp)).tz('Asia/Jakarta').format(format)
  // }
}

export const formatTimeToDescByISO8601 = (time, format = 'DD/MM/YYYY') => {
  const hack = hackTimeForIos(time)
  return dayjs(hack).tz('Asia/Jakarta').format(format)
}

export const futureTimeMinusNowInSeconds = (future) => {
  const hack = hackTimeForIos(future)
  return dayjs(hack).diff(dayjs(), 'seconds')
}

const hackTimeForIos = (time) => {
  return time.replace(/(\+\d{2})(\d{2})$/, '$1:$2') //hack for low ios, +xxxx=>+xx:xx
}

export const getTimeToDesc = (format = 'DD/MM/YYYY') => {
  const timestamp = new Date().getTime()
  return dayjs(Number(timestamp)).tz('Asia/Jakarta').format(format)
}

export const getDateAfterNDays = (dayNum, format = 'DD/MM/YYYY') => {
  const now = new Date().getTime()
  return dayjs(Number(now)).add(dayNum, 'day').tz('Asia/Jakarta').format(format)
}

export const formatAmountCurrency = (amount, decimal = 'currency') => {
  const IDR = new Intl.NumberFormat('id-ID', {
    style: decimal,
    currency: 'IDR',
    maximumFractionDigits: 0,
    minimumFractionDigits: 0
  })

  return IDR.format(amount).replace(/\s+/g, '')
}

export const formatAmountCurrencyWithoutUnit = (amount, fractionDigits = 0, spacerSymbol = '.') => {
  const defaultFormat = new Intl.NumberFormat('en-US', {
    style: 'decimal',
    maximumFractionDigits: fractionDigits,
    minimumFractionDigits: fractionDigits
  })

  return defaultFormat.format(amount).replace(/,/g, spacerSymbol)
}

export function between(v, min, max) {
  return max <= min ? min : Math.min(max, Math.max(min, v))
}

export const formatBankNumberMask = (val, headSize = 3, tailSize = 4, hasInterval = false) => {
  if (val.length < headSize + tailSize) {
    return val
  } else {
    let maskLength = val.length - headSize - tailSize
    let maskStr = '*'.repeat(maskLength)
    let res = val.substr(0, headSize) + maskStr + val.substr(val.length - tailSize, val.length)
    if (hasInterval) {
      let resList = res.match(/.{1,4}/g)
      res = resList.join(' ')
    }
    return res
  }
}

export const formatPhoneNumberMask = (phone, startRetain, endRetain) => {
  if (phone.length <= startRetain + endRetain) {
    return phone
  } else {
    return (
      phone.substr(0, startRetain) +
      '*'.repeat(phone.length - startRetain - endRetain) +
      phone.substr(phone.length - endRetain, phone.length)
    )
  }
}

export const formatAmountTransformNumber = (amount = '') => {
  return parseInt(amount.replace(/\./g, ''))
}

export function formatNumber(num) {
  let numLen = ((n) => {
    const numStr = n + ''
    if (numStr.includes('.')) {
      const decimalPart = numStr.split('.')[1]
      return decimalPart.length
    } else {
      return 0
    }
  })(num)
  numLen = numLen - 2 > 0 ? numLen - 2 : 0
  console.log(numLen)
  return (num * 100).toFixed(numLen)
}
