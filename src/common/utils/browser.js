const u = navigator.userAgent.toLowerCase()

export const browser = {
  iOS: !!u.match(/\(i[^;]+;( u;)? cpu.+mac os x/), // ios终端
  android: /android/i.test(u),
  app: /XMP\//i.test(u),
  windows: /Windows/i.test(u),
  mac: /Macintosh/i.test(u)
}

export const getPlatform = () => {
  if (browser.android) {
    return 'android'
  } else if (browser.iOS) {
    return 'ios'
  } else if (browser.windows) {
    return 'windows'
  } else if (browser.mac) {
    return 'mac'
  } else {
    return 'other'
  }
}
