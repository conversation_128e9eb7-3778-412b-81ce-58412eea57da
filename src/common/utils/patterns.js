// file referenced from docs

const hex = /^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,
  hexa = /^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,
  hexOrHexa = /^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,
  rgb =
    /^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,
  rgba =
    /^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/

export const testPattern = {
  date: (v) => /^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(v),
  time: (v) => /^([0-1]?\d|2[0-3]):[0-5]\d$/.test(v),
  fulltime: (v) => /^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(v),
  timeOrFulltime: (v) => /^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(v),
  email: (v) => /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[A-Za-z]{2,6}$/.test(v),

  hexColor: (v) => hex.test(v),
  hexaColor: (v) => hexa.test(v),
  hexOrHexaColor: (v) => hexOrHexa.test(v),

  rgbColor: (v) => rgb.test(v),
  rgbaColor: (v) => rgba.test(v),
  rgbOrRgbaColor: (v) => rgb.test(v) || rgba.test(v),

  hexOrRgbColor: (v) => hex.test(v) || rgb.test(v),
  hexaOrRgbaColor: (v) => hexa.test(v) || rgba.test(v),
  anyColor: (v) => hexOrHexa.test(v) || rgb.test(v) || rgba.test(v),

  //业务相关
  password: (v) => /^(?=.*[a-zA-Z]+)(?=.*[0-9]+)[a-zA-Z0-9]{6,}$/.test(v),
  OTP: (v) => /^\d{6}$/.test(v),
  monthlyIncome: (v) => /^[^0]\d{0,8}$/.test(v),
  paymentPassword: (v) => /^\d{6}$/.test(v), // 支付密码

  /**
   * @description: 是否合法姓名
   * @return {*}
   */
  isName: (v) => /^[ ,.\-'`^a-zA-Z]{2,50}$/.test(v),
  /**
   * @description: 08开头，10~13位手机号
   * @return {*}
   */
  isMobile: (v) => /^08\d{8,11}$/.test(v),
  /**
   * @description: 是否Email
   * @return {*}
   */
  isEmail: (v) => /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(v),
  /**
   * @description: 1~20位数字
   * @return {*}
   */
  isCompanyPhone: (v) => /^\d{1,20}$/.test(v),
  /**
   * @description: 1-200未数字字母和空格的组合
   * @return {*}
   */
  isSchool: (v) => /^[a-zA-Z0-9\s]{1,200}$/.test(v),

  /**
   * @description: ASCII码 20-7E 即 空格到~
   * @return {*}
   */
  isValidChar: (v) => /^[ -~]+$/.test(v)
}
