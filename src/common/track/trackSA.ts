import { browser } from '../utils'

enum TrackSAEvent {
  Click = 'h5_clk',
  Imp = 'h5_element_imp',
  Input = 'h5_input',
  Auth = 'h5_auth'
}

interface ITrackExtraData {
  [propName: string]: string
}

interface ITrackSAData {
  tgt_event_id: string
  tgt_name?: string
  page: string
  params: Array<string>
  extraParams?: ITrackExtraData
}

interface ITrackAuthData {
  auth_tgt: string
  auth_tgt_name: string
  page: string
  ret_code: string
  error_code: string
  error_msg: string
}

// const appName = import.meta.env.VITE_APPNAME.toLowerCase()
// const formatAppNameToString = (str: string, joiner = '-') => {
//   return `${appName}${joiner}${str}`
// }

const trackSAClick = (data: ITrackSAData) => {
  track(data, TrackSAEvent.Click)
}

const trackSAImp = (data: ITrackSAData) => {
  track(data, TrackSAEvent.Imp)
}

const trackSAAuth = (data: ITrackAuthData) => {
  const newData: ITrackSAData = {
    tgt_event_id: '',
    tgt_name: '',
    page: data.page,
    params: [],
    extraParams: {
      ...data
    }
  }
  track(newData, TrackSAEvent.Auth)
}

const trackSAInput = (data: ITrackSAData) => {
  track(data, TrackSAEvent.Input)
}

const track = (data: ITrackSAData, type: TrackSAEvent) => {
  const { tgt_event_id, tgt_name, page } = data
  try {
    const outputParams = {
      ...Object.fromEntries(
        data.params.map((o: string, i: number) => [`param${i === 0 ? '' : i}`, o ? String(o) : ''])
      ),
      ...data.extraParams
    }
    const obj = {
      tgt_event_id,
      tgt_name,
      page,
      host: browser.app ? 'app' : 'h5',
      ...outputParams
    }
    console.log(obj)
    window.sensors.track(type, obj)
  } catch (error) {
    console.error(error)
  }
}

export { trackSAClick, trackSAImp, trackSAAuth, trackSAInput }
