import { browser } from '@/common/utils'

export class TrackEvent {
  appName = import.meta.env.VITE_APPNAME.toLowerCase()
  host = browser.app ? 'app' : 'h5'

  constructor(options = {}) {}

  #EVENT_CLICK = 'h5_clk'
  #EVENT_IMPRESSION = 'h5_element_imp'
  #EVENT_INPUT = 'h5_input'
  #EVENT_AUTH = 'h5_auth'

  #formatAppNameToString(str, joiner = '-') {
    return `${this.appName}${joiner}${str}`
  }

  /**
   * @description: 埋点
   * @param {Object} inParams
   * @param {String} inParams.targetType
   * @param {String} inParams.targetID
   * @param {String} inParams.targetName
   * @param {String} inParams.pageName
   * @param {String[]} inParams.params
   * @param {Object.<string,string>} inParams.extraParams
   * @return {*}
   */
  track(inParams, needAppPrefix = true) {
    try {
      const { targetType, targetID = '', targetName, pageName, params = [], extraParams = {} } = inParams

      const outputParams = {
        ...Object.fromEntries(
          params
            //.concat([browser.app ? 'app' : 'h5'])
            .map((o, i) => [`param${i === 0 ? '' : i}`, o ? String(o) : ''])
        ),
        ...extraParams
      }
      console.log(inParams)

      sensors.track(targetType, {
        tgt_event_id:
          needAppPrefix && !targetID.startsWith(this.appName)
            ? this.#formatAppNameToString(targetID, '_')
            : targetID,
        tgt_id:
          needAppPrefix && !targetID.startsWith(this.appName)
            ? this.#formatAppNameToString(targetID, '_')
            : targetID, // 由于越南使用的是tgt_id, 为了避免后续查询带来的问题tgt_event_id和tgt_id都埋入，（补充：历史，不作调整了）
        tgt_name:
          needAppPrefix && !targetID.startsWith(this.appName)
            ? this.#formatAppNameToString(targetName, '-')
            : targetName,
        page:
          needAppPrefix && !targetID.startsWith(this.appName)
            ? this.#formatAppNameToString(pageName, '-')
            : pageName,
        host: this.host,
        ...outputParams
      })
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * @description: 埋点击事件
   * @param {Object} inParams
   * @param {String} inParams.targetID
   * @param {String} inParams.targetName
   * @param {String} inParams.pageName
   * @param {String[]} inParams.params
   * @param {Object.<string,string>} inParams.extraParams
   * @return {*}
   */
  trackClick(inParams, needAppPrefix) {
    this.track(
      {
        ...inParams,
        targetType: this.#EVENT_CLICK
      },
      needAppPrefix
    )
  }

  /**
   * @description: 埋曝光事件
   * @param {Object} inParams
   * @param {String} inParams.targetID
   * @param {String} inParams.targetName
   * @param {String} inParams.pageName
   * @param {String[]} inParams.params
   * @param {Object.<string,string>} inParams.extraParams
   * @return {*}
   */
  trackImpression(inParams, needAppPrefix) {
    this.track(
      {
        ...inParams,
        targetType: this.#EVENT_IMPRESSION
      },
      needAppPrefix
    )
  }

  /**
   * @description: 埋input事件
   * @param {Object} inParams
   * @param {String} inParams.targetID
   * @param {String} inParams.targetName
   * @param {String} inParams.pageName
   * @param {String[]} inParams.params
   * @param {Object.<string,string>} inParams.extraParams
   * @return {*}
   */
  trackInput(inParams, needAppPrefix) {
    this.track(
      {
        ...inParams,
        targetType: this.#EVENT_INPUT
      },
      needAppPrefix
    )
  }

  /**
   * @description: 埋auth事件
   * @param {Object} inParams
   * @param {String} inParams.targetID
   * @param {String} inParams.targetName
   * @param {String} inParams.pageName
   * @param {String} inParams.retCode
   * @param {String} inParams.errMsg
   * @param {String} inParams.errCode
   * @return {*}
   */
  trackAuth(inParams, needAppPrefix) {
    try {
      const { targetID = '', targetName, pageName, retCode, errMsg, errCode } = inParams
      sensors.track(this.#EVENT_AUTH, {
        auth_tgt:
          needAppPrefix && !targetID.startsWith(this.appName)
            ? this.#formatAppNameToString(targetID, '_')
            : targetID,
        auth_tgt_name:
          needAppPrefix && !targetName.startsWith(this.appName)
            ? this.#formatAppNameToString(targetName, '-')
            : targetName,
        page:
          needAppPrefix && !targetID.startsWith(this.appName)
            ? this.#formatAppNameToString(pageName, '-')
            : pageName,
        ret_code: retCode,
        err_msg: errMsg,
        err_code: errCode
      })
    } catch (error) {
      console.error(error)
    }
  }

  autoTrack(params = {}) {
    sensors.quick('autoTrackSinglePage', params)
  }

  trackScreen(screenName, needAppPrefix = true) {
    this.autoTrack({
      $screen_name:
        needAppPrefix && !screenName.startsWith(this.appName)
          ? this.#formatAppNameToString(screenName, '-')
          : screenName,
      host: this.host
    })
  }
}

export const trackEventInstance = new TrackEvent()
