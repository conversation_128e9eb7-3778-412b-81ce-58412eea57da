<template>
  <div
    class="header-wrap app-style-bg-color"
    :style="backgroundColor"
    :class="{ 'header-fixed': fixed, 'bottom-border': bottomBorder }"
  >
    <div v-if="showBack" class="arrow-left" :class="{ 'thin-side-area': !rightButtonText }" @click="goBack">
      <img v-if="brightMode" src="./assets/black_back_arrow.png" />
      <img v-if="!brightMode" src="./assets/white_back_arrow.png" />
    </div>
    <div :class="{ title: true, drakTitleColor: !brightMode }">
      <slot></slot>
    </div>
    <div
      class="right-button"
      :class="{ 'thin-side-area': !rightButtonText }"
      :style="rightButtonStyle"
      @click="rightButtonClick"
    >
      {{ rightButtonText }}
      <div>
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { browser } from '@/common/utils'

export default {
  name: 'AppHeader',

  props: {
    title: {
      type: String
    },
    back: {
      type: Function
    },
    fixed: {
      type: Boolean
    },
    showBack: {
      type: Boolean,
      default: true
    },
    backText: {
      type: String,
      default: ''
    },
    brightMode: {
      type: Boolean,
      default: true
    },
    startColor: {
      type: String
    },
    endColor: {
      type: String
    },
    bgColor: {
      type: String
    },
    bottomBorder: {
      type: Boolean,
      default: false
    },
    rightButtonText: {
      type: String,
      default: ''
    },
    rightButtonColor: {
      type: String,
      default: '#333'
    }
  },
  computed: {
    backgroundColor() {
      if (this.startColor && this.endColor) {
        return 'background-image: linear-gradient(to bottom, ' + this.startColor + ', ' + this.endColor + ')'
      } else if (this.bgColor) {
        return 'background-color:' + this.bgColor
      } else {
        return 'background-image:none'
      }
    },
    rightButtonStyle() {
      return 'color:' + this.rightButtonColor
    }
  },
  methods: {
    goBack(e) {
      this.$emit('backClick')

      if (this.back) {
        return this.back(e)
      } else {
        if (browser.app) {
          this.$bridge.back()
        } else {
          history.back()
          // const { $router, $route } = document.getElementById('app').__vue__;
          // $router.back();
        }
      }
    },
    rightButtonClick(e) {
      this.$emit('rightButtonClick')
    }
  }
}
</script>

<style lang="scss" scoped>
.bottom-border {
  border-bottom: 0.5px solid #ddd;
}

.header-wrap {
  /* prettier-ignore */
  height: 48PX;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  &.header-fixed {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 100;
    transform: translateZ(1px);
  }
  .title {
    margin: 0 10px;
    font-family: OpenSans-SemiBold, OpenSans;
    /* prettier-ignore */
    font-size: 18PX;
    color: #333333;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1;
    padding: 0 4px;
  }
  .drakTitleColor {
    color: white;
  }
  .arrow-left {
    height: 100%;
    width: 23%;
    text-align: center;
    /* prettier-ignore */
    padding-left: 14PX;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    img {
      /* prettier-ignore */
      width: 10PX;
    }
  }
  .right-button {
    height: 100%;
    width: 23%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 14px;
    font-size: 13px;
    line-height: 15px;
  }
  .thin-side-area {
    width: 10%;
  }
}
</style>
