<template>
  <div class="image-container">
    <div class="wrap_padding">
      <img class="camera-img" :src="backgroundImg" alt="camera-img" />
      <canvas :id="id" class="img-canvas"></canvas>
      <template v-if="model">
        <img class="img-checked" :src="require('./assets/img_checked.png')" />
      </template>
      <input
        id="img-input"
        ref="inputImg"
        class="img-input"
        type="file"
        :accept="accept"
        :capture="capture"
        name="inputImg"
        @click="onClkTakePhoto"
        @change="onTakePhoto"
      />
    </div>
  </div>
</template>

<script>
// import { uploadFile } from '@/api';
import { randomString } from '@/common/utils/helper'
const UploadImageQuality = 0.3
const UploadImageMaxPixel = 1280
import { imageResizeAndCompress } from '@/common/utils'
import { InToast } from '@/common/components/InToast'

export default {
  name: 'InImageUploader',

  components: {},

  props: {
    backgroundImg: {},
    capture: {},
    value: {},
    accept: {
      type: String,
      default: 'image/*'
    },
    uploadFunc: {
      type: Function,
      default: () => {}
    },
    onOcrResult: {
      type: Function,
      default: () => {}
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      id: randomString(),
      multiImgSrcMap: {}
    }
  },

  computed: {
    model: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },

  watch: {
    model: {
      immediate: true,
      handler(val) {
        if (val) {
          const image = new Image()
          image.src = val
          image.onload = () => {
            const canvasElement = document.getElementById(this.id)
            this.drawImageToCanvas(image, canvasElement)
          }
        }
      }
    }
  },

  created() {},

  methods: {
    onClkTakePhoto(e) {
      if (this.readonly) {
        e.preventDefault()
      }
      this.$emit('take-photo-click')
    },

    // 拍照文件回调
    async onTakePhoto(event) {
      let file = event.target.files[0]
      console.log(file.type)
      console.log(file)
      console.log()
      if (!file) return

      console.log('开始压缩图片')
      imageResizeAndCompress(file, UploadImageQuality, UploadImageMaxPixel, true, async (file) => {
        console.log('开始上传图片')
        const res = await this.uploadFunc(file)
        console.log(`上传图片结束,result=${res.result}`)
        if (this.onOcrResult) {
          const url = this.onOcrResult(res) //由于不同上传接口的返回值不同，所以我们让调用方解析返回值，并决定是否接受这张照片
          if (url) {
            this.fileToCanvas(file)
            this.model = url
          }
        }

        this.$refs.inputImg.value = null
      })
    },

    // 上传成功后转成src
    fileToCanvas(file) {
      let reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = (event) => {
        let image = new Image()
        image.src = String(event.target.result)
        if (!this.showMultiPhoto) {
          image.onload = () => {
            let canvasElement = document.getElementById(this.id)
            this.drawImageToCanvas(image, canvasElement)
          }
        }
      }
    },

    // 上传成功后绘制到canvas上
    drawImageToCanvas(image, canvasElement) {
      let screenScale = window.devicePixelRatio
      let imageCanvas = canvasElement.getContext('2d')
      canvasElement.width = imageCanvas.canvas.scrollWidth * screenScale
      canvasElement.height = imageCanvas.canvas.scrollHeight * screenScale
      let ratio = image.width / image.height
      let canvasRatio = canvasElement.width / canvasElement.height

      let xStart = 0
      let yStart = 0
      let renderableWidth
      let renderableHeight
      if (ratio > canvasRatio) {
        // 横向过大
        let hRatio = image.height / canvasElement.height
        renderableHeight = image.height
        renderableWidth = canvasElement.width * hRatio
        xStart = (image.width - renderableWidth) / 2
      } else if (ratio < canvasRatio) {
        // 横向过小
        let wRatio = image.width / canvasElement.width
        renderableWidth = image.width
        renderableHeight = canvasElement.height * wRatio
        yStart = (image.height - renderableHeight) / 2
      }
      imageCanvas.drawImage(
        image,
        xStart,
        yStart,
        renderableWidth,
        renderableHeight,
        0,
        0,
        canvasElement.width,
        canvasElement.height
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.image-container {
  position: relative;
  padding: 6px;
  box-sizing: border-box;
  background: #dcf6e1;
  border-radius: 6px;

  .wrap_padding {
    position: relative;

    .img-checked {
      width: 32px;
      height: 26px;
      position: absolute;
      top: 0;
      right: 0;
    }

    .img-input {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
    }

    .img-canvas {
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }

    .camera-img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
