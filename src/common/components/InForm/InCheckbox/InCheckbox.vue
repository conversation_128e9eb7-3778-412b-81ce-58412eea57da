<template>
  <div :class="['in-checkbox', { 'in-checkbox--checked': checked }]">
    <div
      class="in-checkbox__inner"
      :style="{ width: width + 'px', height: height + 'px' }"
      @click.stop="handleClick"
    >
      <div class="in-checkbox__bg">
        <svg
          class="in-checkbox__svg"
          :style="{ width: svgWidth + 'px', height: svgHeight + 'px' }"
          viewBox="0 0 24 24"
        >
          <path class="in-checkbox__truthy" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"></path>
          <path class="in-checkbox__indet" d="M4,14H20V10H4"></path>
        </svg>
      </div>
    </div>
    <div class="in-checkbox__label">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InCheckbox',

  components: {},

  props: {
    value: {},
    trueValue: {
      type: Boolean,
      default: true
    },
    falseValue: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 16
    },
    height: {
      type: Number,
      default: 16
    },
    svgWidth: {
      type: Number,
      default: 16
    },
    svgHeight: {
      type: Number,
      default: 16
    }
  },

  data() {
    return {}
  },

  computed: {
    checked() {
      if (this.$listeners.input) {
        return this.value === this.trueValue
      } else {
        return this.$parent.checkedList.includes(this.value)
      }
    }
  },

  created() {},

  methods: {
    handleClick() {
      if (this.$listeners.input) {
        this.$emit('input', this.value === this.trueValue ? this.trueValue : this.falseValue)
      } else {
        this.$parent.handleToggleCheckItem(this.value)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.in-checkbox {
  display: flex;
  align-items: flex-start;
  &__inner {
    position: relative;
    width: 16px;
    height: 16px;
    border-radius: 2px;
    flex-shrink: 0;
    margin-top: 2px;
  }
  &__label {
    font-size: 13px;
    font-family: OpenSans-Regular, OpenSans;
    color: #545454;
    line-height: 20px;
    margin-left: 10px;
  }
  &__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid currentColor;
    border-radius: 2px;
    transition: background 0.22s cubic-bezier(0, 0, 0.2, 1) 0ms;
    -webkit-print-color-adjust: exact;
    color: #dddddd;

    display: flex;
    justify-content: center;
    align-items: center;
  }
  &__svg {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    color: #fff;
  }
  &--checked {
    .in-checkbox__bg {
      background: #0eb966;
      color: #0eb966;
    }
    .in-checkbox__truthy {
      transition: stroke-dashoffset 0.18s cubic-bezier(0.4, 0, 0.6, 1) 0ms;
      stroke-dashoffset: 0;
    }
  }
  &__truthy {
    stroke: #fff;
    stroke-width: 3.12px;
    stroke-dashoffset: 29.78334;
    stroke-dasharray: 29.78334;
  }
  &__indet {
    stroke-dashoffset: 0;
    transition: stroke-dashoffset 0.18s cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    fill: #fff;
    transform-origin: 50% 50%;
    transform: rotate(-280deg) scale(0);
  }
}
</style>
