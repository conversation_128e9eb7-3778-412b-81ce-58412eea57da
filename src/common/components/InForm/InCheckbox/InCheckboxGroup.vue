<template>
  <div class="in-checkbox-group">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'InCheckboxGroup',

  components: {},

  props: {
    value: {
      type: Array,
      default: () => []
    },

    max: {},

    single: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {}
  },

  computed: {
    checkedList: {
      get() {
        return this.value
      },

      set(val) {
        this.$emit('input', val)
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    handleToggleCheckItem(value) {
      const index = this.checkedList.findIndex((m) => m === value)
      if (this.single) {
        if (index > -1) {
          this.checkedList.splice(index, 1)
        } else {
          this.checkedList = [value]
        }
      } else {
        if (index > -1) {
          this.checkedList.splice(index, 1)
        } else {
          if (this.max && this.checkedList.length >= this.max) {
            return
          }
          this.checkedList.push(value)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
