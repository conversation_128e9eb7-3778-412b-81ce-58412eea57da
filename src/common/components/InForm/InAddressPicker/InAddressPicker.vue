<template>
  <InInput
    ref="input"
    v-model="displayLabel"
    class="in-address-picker in-validation-component"
    :label="label"
    readonly
    :rules="rules"
    @click="handleInputClick"
  >
    <template #append>
      <img class="arrow-down" :src="require('./assets/arrow-down.png')" />
    </template>
  </InInput>
</template>

<script>
import { InInput } from '../InInput'
import { createDialog } from '@/common/components/InDialog'
import AddressPopup from './AddressPopup.vue'

export default {
  name: 'InDropdown',

  components: {
    InInput
  },

  props: {
    title: {},
    value: {},
    label: {},
    options: {},
    rules: {},
    readonly: {}
  },

  data() {
    return {}
  },

  computed: {
    model: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },

    displayLabel() {
      const l = [this.model.province, this.model.city, this.model.district].filter((m) => m)
      return l.join(',')
    },

    showError() {
      return this.hasError
    }
  },

  created() {},

  methods: {
    handleInputClick() {
      if (this.readonly) return
      createDialog({
        component: AddressPopup,
        parent: this,
        options: this.options,
        currentValue: this.model,
        title: this.title
      }).onOk((data, close) => {
        if (this.model !== data.value) {
          this.$emit('change', data)
        }
        this.model = data
        close()
      })
    },

    validate() {
      return this.$refs.input.validate()
    }
  }
}
</script>

<style lang="scss" scoped>
.arrow-down {
  display: block;
  width: 14px;
  height: 14px;
  margin-right: 15px;
}

.in-address-picker {
  ::v-deep input[readonly] {
    color: #333 !important;
  }
}
</style>
