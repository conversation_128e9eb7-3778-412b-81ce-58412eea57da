<template>
  <InPopup ref="dialog" class="in-address-popup" @hide="handleHide">
    <div class="in-address-popup__wrap">
      <div class="toolbar">
        <div class="name">{{ title }}</div>
        <div class="confirm" :class="{ 'confirm-disable': !value.district }" @click="confirm">
          {{ confrimTitle }}
        </div>
      </div>
      <div class="divider"></div>
      <div class="selected-value-line">
        <div
          class="selected-value"
          :class="{ 'highlight-value': optionListType == 0 }"
          @click="onClickProvince"
        >
          {{ value.province || 'Silakan pilih' }}
        </div>
        <div class="selected-value" :class="{ 'highlight-value': optionListType == 1 }" @click="onClickCity">
          {{ value.city || 'Silakan pilih' }}
        </div>
        <div
          class="selected-value"
          :class="{ 'highlight-value': optionListType == 2 }"
          @click="onClickDistrice"
        >
          {{ value.district || 'Silakan pilih' }}
        </div>
      </div>
      <div class="divider"></div>
      <div class="option-list">
        <div
          v-for="(item, index) in optionList"
          :key="index"
          class="one-option"
          :class="{
            'highlight-value':
              (optionListType == 0 && value.province == item) ||
              (optionListType == 1 && value.city == item) ||
              (optionListType == 2 && value.district == item)
          }"
          @click="optionSelected(item)"
        >
          {{ item }}
        </div>
      </div>
    </div>
  </InPopup>
</template>

<script>
import { InPopup } from '@/common/components/InDialog'
import cloneDeep from 'lodash/cloneDeep'

export default {
  name: 'PopupMenu',

  components: {
    InPopup
  },

  props: {
    options: {},
    currentValue: {},
    title: {},
    placeholder: {
      type: String
    }
  },

  data() {
    return {
      optionListType: 0, // 0=province 1=city 2=district
      value: {
        province: '',
        city: '',
        district: ''
      }
    }
  },

  computed: {
    optionList({ options }) {
      let res = []

      if (this.optionListType === 0) {
        // 选择Province
        Object.keys(options).map((key) => {
          res.push(key)
        })
      } else if (this.optionListType === 1) {
        // 选择City
        Object.keys(options[this.value.province]).map((key) => {
          res.push(key)
        })
      } else {
        // 选择District
        return options[this.value.province][this.value.city]
      }

      return res.sort()
    },
    confrimTitle() {
      if (this.$t) {
        return this.$t('确认')
      } else {
        return 'Konfrimasi'
      }
    }
  },

  created() {
    this.value = cloneDeep(this.currentValue)
  },

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    show() {
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$emit('hide')
    },

    optionSelected(value) {
      if (this.optionListType === 0) {
        // 选择Province
        this.value.province = value
        this.value.city = ''
        this.value.district = ''
        this.optionListType = 1
      } else if (this.optionListType === 1) {
        // 选择City
        this.value.city = value
        this.value.district = ''
        this.optionListType = 2
      } else {
        // 选择District
        this.value.district = value
        this.confirm()
      }
    },
    onClickProvince() {
      this.optionListType = 0
    },
    onClickCity() {
      if (this.value.city) {
        this.optionListType = 1
      }
    },
    onClickDistrice() {
      if (this.value.district) {
        this.optionListType = 2
      }
    },
    confirm() {
      if (this.value.district) {
        console.log(this.value)
        this.$emit('ok', cloneDeep(this.value))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.in-address-popup {
  &__wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    height: auto;
    // height: 100%;
    background: #ffffff;
    border-radius: 14px 14px 0px 0px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    // padding: 16px 20px 0 20px;
    box-sizing: border-box;
    font-family: OpenSans-Regular, OpenSans;
  }
  .divider {
    background-color: #ddd;
    height: 1px;
  }
  .toolbar {
    display: flex;
    height: 40px;
    padding: 0 20px;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;
    .confirm-disable {
      color: #bbb;
    }
  }
  .selected-value-line {
    min-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 20px;
    text-align: left;
    font-size: 13px;
    .selected-value {
      margin-right: 10px;
    }
    .selected-value:last-child {
      margin-right: 0px;
    }
  }
  .option-list {
    padding: 0 20px;
    margin: 10px 0;
    height: 190px;
    overflow: scroll;
    .one-option {
      height: 40px;
      font-size: 12px;
      text-align: left;
    }
  }
  .highlight-value {
    color: #00903b;
  }

  &__header {
    display: flex;
    align-items: center;
    .title {
      font-size: 16px;
      font-family: OpenSans-SemiBold, OpenSans;
      color: #333333;
      line-height: 20px;
      text-align: center;
      flex: 1;
    }
    .close-img {
      width: 16px;
      height: 16px;
    }
  }
  &__content {
    max-height: 400px;
    min-height: 44px;
    overflow: auto;
    margin-top: 5px;
  }
  &__list {
    // padding: 0 19px;
    max-height: 70%;
  }
}

.panel {
  position: absolute;
  width: 100%;
  height: 300px;
  left: 0;
  bottom: 0;
  background-color: #f5f7f6;
  font-size: 16px;
  box-sizing: border-box;
  color: #333;
}
</style>
