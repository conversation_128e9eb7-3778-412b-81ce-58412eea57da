<template>
  <label
    :class="[
      'in-field',
      { 'in-field--float': focused && editable },
      { 'in-field--error': showError },
      { 'in-field--valid': isValid && !focused },
      { 'in-field--label': label },
      { 'in-field--disabled': readonly },
      { 'in-validation-component': !readonly }
    ]"
    :for="uid"
  >
    <div class="in-field__inner">
      <div class="in-field__control">
        <div class="in-field__prepend">
          <slot name="prepend"></slot>
        </div>
        <div class="in-field__control-container">
          <textarea
            v-if="isTextarea"
            :id="uid"
            ref="input"
            v-model="val"
            :class="['in-field__native', { 'in-field__disabled': readonly }]"
            :style="inputStyle"
            v-bind="$attrs"
            :readonly="readonly"
            :type="inputType"
            :maxLength="maxLength"
            :pattern="pattern"
            v-on="onEvents"
          />
          <input
            v-else
            :id="uid"
            ref="input"
            v-model="val"
            :class="['in-field__native', { 'in-field__disabled': readonly }]"
            :style="inputStyle"
            v-bind="$attrs"
            :readonly="readonly"
            :type="inputType"
            :maxLength="maxLength"
            :pattern="pattern"
            v-on="onEvents"
          />
          <div :class="['in-field__label', { 'in-field__label-error': showError && hasValue }]">
            {{ label }}
          </div>
        </div>
        <div class="in-field__append">
          <slot name="append"></slot>
          <i
            v-if="clearable && !readonly && val && val.length > 0"
            class="icon-clear"
            @click="handleClear"
          ></i>

          <p :class="{ 'send-otp': true, 'send-otp__disabled': disableSendOtp }" @click.stop="handleSendOtp">
            {{ sendOtpText }}
          </p>
          <i
            v-if="type === 'password'"
            :class="{
              'icon-pwd-show': showPassword,
              'icon-pwd-hide': !showPassword
            }"
            @click="showPassword = !showPassword"
          ></i>
        </div>
      </div>
      <div v-if="needBottom" class="in-field__bottom">
        <div class="in-field__message">
          <div v-if="showError">{{ computedErrorMessage }}</div>
          <div v-else-if="hint && focused">{{ hint }}</div>
        </div>
      </div>
    </div>
  </label>
</template>

<script>
import { randomString } from '@/common/utils/helper'
import { ValidateMixin } from '../mixins/validate'
import MaskMixin from '../mixins/mask'
import CompositionMixin from '../mixins/composition.js'
import ListenersMixin from '../mixins/listeners.js'
import { stop } from '../utils/event'

export default {
  name: 'InInputOtp',

  components: {},

  mixins: [ValidateMixin, MaskMixin, CompositionMixin, ListenersMixin],
  props: {
    label: {},
    hint: {},
    readonly: {
      type: Boolean,
      default: false
    },
    rules: {},
    clearable: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'tel'
    },
    maxLength: {
      type: Number
    },
    pattern: {
      type: String
    },
    optional: {
      type: Boolean,
      default: false
    },
    inputStyle: {
      type: [Array, String, Object],
      default: ''
    },
    sendOtpText: {
      type: String,
      default: 'Kirim'
    },
    disableSendOtp: {
      type: Boolean,
      default: false
    }
  },

  emits: ['sendOtp'],

  data() {
    return {
      uid: `in_${randomString()}`,
      focused: false,
      showPassword: false,
      innerValue: this.__getInitialMaskedValue(),
      focusTimeStamp: 0
    }
  },

  computed: {
    val: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },

    isTextarea() {
      return this.type === 'textarea'
    },

    editable() {
      return this.readonly !== true
    },

    needBottom() {
      return !!this.hint || this.hasRules
    },

    inputType({ type, showPassword }) {
      return type !== 'password' ? type : showPassword ? 'text' : 'password'
    },

    hasValue() {
      return this.val !== null && this.val !== undefined && this.val !== ''
    },

    isValid() {
      return !this.hasError && (this.hasValue || this.optional)
    },

    showError() {
      return this.hasError && !this.focused && this.hasValue
    },

    onEvents() {
      const on = {
        ...this.qListeners,
        input: this.__onInput,
        paste: this.__onPaste,
        // Safari < 10.2 & UIWebView doesn't fire compositionend when
        // switching focus before confirming composition choice
        // this also fixes the issue where some browsers e.g. iOS Chrome
        // fires "change" instead of "input" on autocomplete.
        change: this.__onChange,
        blur: this.__onFinishEditing,
        focus: this.__onFocus
      }

      on.compositionstart = on.compositionupdate = on.compositionend = this.__onComposition

      if (this.hasMask === true) {
        on.keydown = this.__onMaskedKeydown
      }

      return on
    }
  },

  watch: {
    value(v) {
      if (this.hasMask === true) {
        if (this.stopValueWatcher === true) {
          this.stopValueWatcher = false
          return
        }
        this.__updateMaskValue(v)
      } else if (this.innerValue !== v) {
        this.innerValue = v

        if (this.type === 'number' && this.hasOwnProperty('tempValue') === true) {
          if (this.typedNumber === true) {
            this.typedNumber = false
          } else {
            delete this.tempValue
          }
        }
      }
    }
  },

  mounted() {},

  created() {},

  beforeUnmount() {
    this.__onFinishEditing()
  },

  methods: {
    __onFocus(e) {
      if (this.readonly) return
      this.focused = true
      this.$emit('focus')
      this.focusTimeStamp = new Date().getTime()
      stop(e)
    },

    handleClear() {
      this.val = ''
    },

    __onPaste(e) {
      if (this.hasMask === true && this.reverseFillMask !== true) {
        const inp = e.target
        this.__moveCursorForPaste(inp, inp.selectionStart, inp.selectionEnd)
      }

      this.$emit('paste', e)
    },

    __onInput(e) {
      if (!e || !e.target || e.target.composing === true) {
        return
      }

      if (this.type === 'file') {
        this.$emit('input', e.target.files)
        return
      }

      const val = e.target.value

      if (this.hasMask === true) {
        this.__updateMaskValue(val, false, e.inputType)
      } else {
        this.__emitValue(val)

        if (
          ['text', 'search', 'url', 'tel', 'password'].includes(this.type) &&
          e.target === document.activeElement
        ) {
          const index = e.target.selectionEnd

          index !== void 0 &&
            this.$nextTick(() => {
              if (e.target === document.activeElement && val.indexOf(e.target.value) === 0) {
                e.target.setSelectionRange(index, index)
              }
            })
        }
      }

      // textArea高度自适应
      if (this.type === 'textarea') {
        console.log(e.target.scrollHeight, e.target.scrollHeight)
        e.target.style.height = '0'
        e.target.style.height = e.target.scrollHeight + 'px'
      }
    },

    __emitValue(val, stopWatcher) {
      this.emitValueFn = () => {
        if (this.type !== 'number' && this.hasOwnProperty('tempValue') === true) {
          delete this.tempValue
        }

        if (this.value !== val && this.emitCachedValue !== val) {
          this.emitCachedValue = val

          stopWatcher === true && (this.stopValueWatcher = true)
          this.$emit('input', val)

          this.$nextTick(() => {
            this.emitCachedValue === val && (this.emitCachedValue = NaN)
          })
        }

        this.emitValueFn = void 0
      }

      if (this.type === 'number') {
        this.typedNumber = true
        this.tempValue = val
      }

      if (this.debounce !== void 0) {
        clearTimeout(this.emitTimer)
        this.tempValue = val
        this.emitTimer = setTimeout(this.emitValueFn, this.debounce)
      } else {
        this.emitValueFn()
      }
    },

    __onChange(e) {
      this.__onComposition(e)

      clearTimeout(this.emitTimer)
      this.emitValueFn !== void 0 && this.emitValueFn()

      this.$emit('change', e)
    },

    __onFinishEditing(e) {
      e !== void 0 && stop(e)

      clearTimeout(this.emitTimer)
      this.emitValueFn !== void 0 && this.emitValueFn()

      this.typedNumber = false
      this.stopValueWatcher = false
      delete this.tempValue

      // we need to use setTimeout instead of this.$nextTick
      // to avoid a bug where focusout is not emitted for type date/time/week/...
      this.type !== 'file' &&
        setTimeout(() => {
          if (this.$refs.input !== void 0) {
            this.$refs.input.value = this.innerValue !== void 0 ? this.innerValue : ''
          }
          this.focused = false
          const duration = new Date().getTime() - this.focusTimeStamp
          this.$emit('blur', duration)
        })
    },

    handleSendOtp(e) {
      this.$emit('sendOtp')
    }
  }
}
</script>

<style lang="scss" scoped>
.in-field {
  display: flex;
  &__inner {
    position: relative;
    width: 100%;
    .in-field--label & {
      padding-top: 6px;
    }
  }
  &__control {
    position: relative;
    border-radius: 4px;
    padding-left: 12px;
    display: flex;
    flex-wrap: nowrap;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      pointer-events: none;
      border: 1px solid #ddd;
      border-radius: inherit;
      transition: border-color 0.36s cubic-bezier(0.4, 0, 0.2, 1);
      .in-field--error & {
        border-color: #dd3424;
      }
      .in-field--float & {
        border-color: var(--main-color, #00903b);
      }
    }
  }
  &__control-container {
    position: relative;
    height: inherit;
    flex: 10000 1 0%;
  }
  &__native {
    width: 100%;
    min-width: 0;
    outline: 0 !important;
    text-decoration: inherit;
    text-transform: inherit;
    border: none;
    border-radius: 0;
    background: none;
    outline: 0;
    padding: 14px 0 14px 0;
    font-size: 16px;
    height: 48px;
    line-height: 20px;
    color: #424147;
    font-family: OpenSans-SemiBold, OpenSans;
    box-sizing: border-box;
    &::-webkit-input-placeholder {
      font-size: 16px;
      font-family: OpenSans-Regular, OpenSans;
      color: #888;
      line-height: 20px;
      padding-left: 3px;
    }
  }
  &__label {
    @include text-ellipsis();
    position: absolute;
    left: 0px;
    top: 14px;
    color: #c4c4c4;
    max-width: 100%;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0.00937em;
    text-decoration: inherit;
    text-transform: inherit;
    transform-origin: left top;
    transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.324s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.324s;
    background-color: #fff;
    font-family: OpenSans-Regular, OpenSans;

    .in-field--float & {
      color: var(--main-color, #00903b);
      transform: translateY(-100%) scale(0.75);
      transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.396s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.324s;
    }

    .in-field--error & {
      color: #dd3424;
      transform: translateY(-100%) scale(0.75);
      transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.396s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.324s;
    }
    .in-field--valid & {
      color: #c4c4c4;
      transform: translateY(-100%) scale(0.75);
      transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.396s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.324s;
    }
  }
  // &__label-error {
  //   color: #dd3424 !important;
  // }
  &__disabled {
    color: #939393;
  }

  &__bottom {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
    min-height: 12px;
    padding: 1px 8px 0 8px;
  }
  &__message {
    font-size: 10px;
    font-family: AlibabaSansViet;
    color: #888;
    line-height: 12px;
    .in-field--error & {
      color: #dd3424;
    }
  }
  &__append {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-left: 14px;
  }
  &__prepend {
    position: relative;
  }
}

.icon-clear {
  @include icon();
  background-image: url('./assets/images/icon-clear.png');
  width: 14px;
  height: 14px;
  margin-right: 19px;
}
.icon-pwd-show {
  @include icon();
  background-image: url('./assets/images/icon-pwd-show.png');
  width: 18px;
  height: 18px;
  margin-right: 12px;
}
.icon-pwd-hide {
  @include icon();
  background-image: url('./assets/images/icon-pwd-hide.png');
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

.send-otp {
  font-size: 12px;
  font-weight: 600;
  // line-height: 14px;
  color: #068bfd;
  margin-right: 12px;
  line-height: 47px;
}

.send-otp__disabled {
  color: #adadad;
}
</style>
