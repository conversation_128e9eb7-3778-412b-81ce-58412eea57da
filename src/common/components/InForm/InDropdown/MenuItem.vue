<template>
  <div :class="['in-dropdown-menu-item', { 'in-dropdown-menu-item--active': isActive }]">
    <span>{{ label }}</span>
    <img v-if="isActive" class="checked" :src="require('./assets/check.png')" />
  </div>
</template>

<script>
export default {
  name: 'MenuItem',

  components: {},

  props: {
    label: {},
    value: {},
    isActive: {}
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.in-dropdown-menu-item {
  height: 50px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  &--active {
    span {
      color: #00903b !important;
      font-family: OpenSans-SemiBold, OpenSans !important;
    }
  }
  span {
    font-size: 14px;
    font-family: OpenSans-Regular, OpenSans;
    line-height: 20px;
    color: #333333;
  }
  &:after {
    content: ' ';
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px; // ignored
    border-bottom: 1px solid #f8f8f8; // ignored
    color: #f8f8f8;
    transform-origin: 0 100%;
    transform: scaleY(0.5);
  }
  .checked {
    width: 14px;
    height: 10px;
  }
}
</style>
