<template>
  <InInput
    ref="input"
    v-model="displayLabel"
    class="in-dropdown in-validation-component"
    :label="label"
    readonly
    :rules="rules"
    @click="handleInputClick"
  >
    <template #append>
      <img class="arrow-down" :src="require('./assets/arrow-down.png')" />
    </template>
  </InInput>
</template>

<script>
import { InInput } from '../InInput'
import { createDialog } from '@/common/components/InDialog'
import PopupMenu from './PopupMenu.vue'

export default {
  name: 'InDropdown',

  components: {
    InInput
  },

  props: {
    title: {},
    value: {},
    label: {},
    options: {},
    rules: {}
  },

  data() {
    return {}
  },

  computed: {
    model: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },

    displayLabel({ options }) {
      const item = options ? options.find((m) => m.value === this.model) : null
      if (item) {
        return item.label
      }
      return ''
    },

    showError() {
      return this.hasError
    }
  },

  created() {},

  methods: {
    handleInputClick() {
      createDialog({
        component: PopupMenu,
        parent: this,
        options: this.options,
        currentValue: this.model,
        title: this.title
      }).onOk((data, close) => {
        this.model = data.value
        if (this.model !== data.value) {
          this.$emit('change', data)
        }
        close()
      })
    },

    validate() {
      return this.$refs.input.validate()
    }
  }
}
</script>

<style lang="scss" scoped>
.arrow-down {
  display: block;
  width: 14px;
  height: 14px;
  margin-right: 15px;
}
.in-dropdown {
  ::v-deep input[readonly] {
    color: #333 !important;
  }
}
</style>
