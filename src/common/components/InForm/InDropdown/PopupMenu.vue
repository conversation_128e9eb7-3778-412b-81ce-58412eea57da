<template>
  <InPopup ref="dialog" class="in-dropdown-menu" @hide="handleHide">
    <div class="in-dropdown-menu__wrap">
      <div class="in-dropdown-menu__header">
        <div class="title">{{ title }}</div>
        <img :src="require('./assets/close.png')" class="close-img" @click="hide" />
      </div>
      <div class="in-dropdown-menu__content">
        <div class="in-dropdown-menu__list">
          <MenuItem
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            :is-active="currentValue == item.value"
            @click.native="handleMenuClick(item)"
          />
        </div>
      </div>
    </div>
  </InPopup>
</template>

<script>
import { InPopup } from '@/common/components/InDialog'
import MenuItem from './MenuItem.vue'

export default {
  name: 'PopupMenu',

  components: {
    InPopup,
    MenuItem
  },

  props: {
    options: {},
    currentValue: {},
    title: {},
    onMounted: {},
    onClose: {}
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  mounted() {
    this.onMounted && this.onMounted()
  },

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    show() {
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$emit('hide')
      this.onClose && this.onClose()
    },

    handleMenuClick(item) {
      this.$emit('ok', item)
    }
  }
}
</script>

<style lang="scss" scoped>
.in-dropdown-menu {
  &__wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    height: auto;
    // height: 100%;
    background: #ffffff;
    border-radius: 14px 14px 0px 0px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    padding: 16px 20px 0 20px;
    padding: 16px 20px env(safe-area-inset-bottom) 20px;
    box-sizing: border-box;
  }
  &__header {
    display: flex;
    align-items: center;
    .title {
      font-size: 16px;
      font-family: OpenSans-SemiBold, OpenSans;
      color: #333333;
      line-height: 20px;
      text-align: center;
      flex: 1;
    }
    .close-img {
      // position: absolute;
      // top: 16px;
      // right: 20px;
      width: 16px;
      height: 16px;
    }
  }
  &__content {
    max-height: 400px;
    min-height: 44px;
    overflow: auto;
    margin-top: 5px;
  }
  &__list {
    // padding: 0 19px;
    max-height: 70%;
  }
}
</style>
