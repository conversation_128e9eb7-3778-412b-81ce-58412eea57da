<template>
  <div v-touch-pan.prevent.stop="editable ? __pan : null" class="in-slider">
    <div class="in-slider__track-container">
      <div class="in-slider__track">
        <div class="in-slider__inner"></div>
        <div
          class="in-slider__selection"
          :style="{
            width: `${100 * ratio}%`
          }"
        ></div>
        <div
          class="in-slider__thumb-wrap"
          :style="{
            left: `${100 * ratio}%`
          }"
        >
          <div class="in-slider__thumb"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TouchPan from './TouchPan'
import { between } from '@/common/utils/format'
import { position } from '../utils/event'

export default {
  name: 'InSlider',

  directives: {
    TouchPan
  },

  components: {},

  props: {
    value: {},
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    },
    step: {
      type: Number,
      default: 1,
      validator: (v) => v >= 0
    },
    disable: {
      type: Boolean
    },
    readonly: {
      type: Boolean
    }
  },

  data() {
    return {
      active: false,
      model: this.value === null ? this.min : this.value,
      curRatio: 0
    }
  },

  computed: {
    editable() {
      return this.disable !== true && this.readonly !== true && this.min < this.max
    },

    ratio() {
      return this.active === true ? this.curRatio : this.modelRatio
    },

    modelRatio() {
      return this.minMaxDiff === 0 ? 0 : (this.model - this.min) / this.minMaxDiff
    },

    minMaxDiff() {
      return this.max - this.min
    },

    decimals() {
      return (String(this.step).trim('0').split('.')[1] || '').length
    },

    computedStep() {
      return this.step === 0 ? 1 : this.step
    }
  },

  watch: {
    value(v) {
      this.model = v === null ? 0 : between(v, this.min, this.max)
    },

    min(v) {
      this.model = between(this.model, v, this.max)
    },

    max(v) {
      this.model = between(this.model, this.min, v)
    }
  },

  created() {},

  methods: {
    getRatio(evt, dragging, reverse, vertical) {
      const pos = position(evt),
        val =
          vertical === true
            ? between((pos.top - dragging.top) / dragging.height, 0, 1)
            : between((pos.left - dragging.left) / dragging.width, 0, 1)

      return reverse === true ? 1.0 - val : val
    },

    getModel(ratio, min, max, step, decimals) {
      let model = min + ratio * (max - min)

      if (step > 0) {
        const modulo = (model - min) % step
        model += (Math.abs(modulo) >= step / 2 ? (modulo < 0 ? -1 : 1) * step : 0) - modulo
      }

      if (decimals > 0) {
        model = parseFloat(model.toFixed(decimals))
      }

      return between(model, min, max)
    },

    __updateValue(change) {
      if (this.model !== this.value) {
        this.$emit('input', this.model)
      }
      change === true && this.$emit('change', this.model)
    },

    __getDragging() {
      return this.$el.getBoundingClientRect()
    },

    __updatePosition(event, dragging = this.dragging) {
      const ratio = this.getRatio(event, dragging, this.isReversed, this.vertical)

      this.model = this.getModel(ratio, this.min, this.max, this.step, this.decimals)
      this.curRatio =
        this.snap !== true || this.step === 0
          ? ratio
          : this.minMaxDiff === 0
          ? 0
          : (this.model - this.min) / this.minMaxDiff
    },

    __pan(event) {
      if (event.isFinal) {
        if (this.dragging !== void 0) {
          this.__updatePosition(event.evt)
          // only if touch, because we also have mousedown/up:
          event.touch === true && this.__updateValue(true)
          this.dragging = void 0
          this.$emit('pan', 'end')
        }
        this.active = false
      } else if (event.isFirst) {
        this.dragging = this.__getDragging(event.evt)
        this.__updatePosition(event.evt)
        this.__updateValue()
        this.active = true
        this.$emit('pan', 'start')
      } else {
        this.__updatePosition(event.evt)
        this.__updateValue()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.in-slider {
  &__track-container {
    cursor: grab;
    width: 100%;
    padding: 12px 0;
    outline: 0;
  }
  &__track {
    position: relative;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    width: inherit;
    height: 5px;
  }
  &__inner {
    position: absolute;
    left: 0%;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    border-radius: inherit;
  }
  &__selection {
    position: absolute;
    left: 0%;
    width: 40%;
    // will-change: width, left;
    // transition: width 0.28s, left 0.28s, right 0.28s, height 0.28s, top 0.28s, bottom 0.28s;
    background: #029640;
    background: linear-gradient(90deg, #029640 0%, #00b84b 103%);
    border-radius: inherit;
    height: 100%;
  }
  &__thumb-wrap {
    left: 40%;
    height: 36px;
    width: 36px;
    position: absolute;
    z-index: 1001;
    top: -15px;
    transform: translateX(-50%);
    background-color: transparent;
    text-align: center;
    user-select: none;
    line-height: normal;
    // transition: left 0.28s, right 0.28s;
    // will-change: left;
    &:after {
      display: inline-block;
      vertical-align: middle;
      content: '';
      height: 100%;
    }
  }
  &__thumb {
    user-select: none !important;
    z-index: 1;
    outline: 0;
    display: inline-block;
    vertical-align: middle;
    width: 22px;
    height: 22px;
    border: 4px solid #2fe259;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 50%;
    backdrop-filter: blur(16.32px);
    box-shadow: 0px 4px 5px 0px rgba(17, 206, 86, 0.3);
  }
}
</style>
