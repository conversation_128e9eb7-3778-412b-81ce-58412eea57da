<template>
  <transition name="fade">
    <div v-show="visible" class="in-toast">
      <div class="in-toast__mask"></div>
      <div class="in-toast__main">
        <div class="in-toast__inner">
          <p class="in-toast-text">{{ message }}</p>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'InToast',

  components: {},

  data() {
    return {
      type: 'info',
      duration: 2000,
      visible: false,
      message: '',
      timer: null
    }
  },

  computed: {},

  created() {},

  methods: {
    close() {
      this.visible = false
    },

    clearTimer() {
      clearTimeout(this.timer)
    },

    startTimer() {
      if (this.duration > 0) {
        this.clearTimer()
        this.timer = setTimeout(() => {
          this.close()
          this.$emit('closed')
        }, this.duration)
      }
    },

    show() {
      this.visible = true
      this.startTimer()
    }
  }
}
</script>

<style lang="scss" scoped>
.in-toast {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  transform: translateZ(2px);
  &__mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 300;
    background: transparent;
  }

  &__main {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 320;
  }
  &__inner {
    position: relative;
    width: 260px;
    margin: 0 auto;
    padding: 10px 15px;
    color: #fff;
    font-size: 14px;
    border-radius: 8px;
    word-break: break-word;
    word-wrap: break-word;
    background: rgba(0, 0, 0, 0.69);
    box-sizing: border-box;
  }
  .in-toast-text {
    display: inline-block;
    line-height: 22px;
    text-align: left;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
</style>
