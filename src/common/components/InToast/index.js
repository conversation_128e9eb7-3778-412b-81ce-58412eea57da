import Vue from 'vue'
import toast from './InToast.vue'

let instance

const ToastConstructor = Vue.extend(toast)

const initInstance = () => {
  instance = new ToastConstructor({
    el: document.createElement('div')
  })
  document.body.appendChild(instance.$el)
}

const InToast = function (option, callback) {
  if (!instance) {
    initInstance()
  }
  instance.type = option.type
  instance.message = option.message
  instance.duration = option.duration || 2000
  instance.$off('closed')
  if (callback) {
    instance.$on('closed', callback)
  }

  Vue.nextTick(() => {
    instance.show()
  })
}

InToast.info = (message, callback) => {
  InToast(
    {
      message,
      type: 'info'
    },
    callback
  )
}

InToast.close = () => {
  Vue.nextTick(() => {
    instance.visible = false
  })
}

export { InToast }
