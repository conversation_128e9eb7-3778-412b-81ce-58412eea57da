<template>
  <transition name="fade">
    <div v-show="visible" class="lx-load-mark">
      <div class="lx-load-box">
        <div class="lx-loading">
          <Spinner />
        </div>
        <div v-if="message" class="lx-load-content">{{ message }}</div>
      </div>
    </div>
  </transition>
</template>

<script>
import Spinner from './Spinner.vue'

export default {
  name: 'InLoading',

  components: {
    Spinner
  },

  data() {
    return {
      visible: true,
      message: ''
    }
  },

  computed: {},

  created() {},

  methods: {
    show() {
      this.visible = true
    },

    close() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
@keyframes show-toast {
  from {
    opacity: 0;
    transform: translate(-50%, -10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.lx-load-mark {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  /* prettier-ignore */
  transform: translateZ(1PX);
  overflow: hidden;
}

.lx-load-box {
  position: fixed;
  z-index: 1002;
  min-width: 96px;
  min-height: 96px;
  max-width: 144px;
  top: 45.5%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  text-align: center;
  border-radius: 5px;
  color: #ffffff;

  display: flex;
  flex-direction: column;
  justify-content: center;
}

.lx-loading {
}
.lx-load-content {
  margin-top: 12px;
}

.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-leave-to {
  opacity: 0;
}
</style>
