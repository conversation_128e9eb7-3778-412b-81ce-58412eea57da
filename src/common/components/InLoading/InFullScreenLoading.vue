<template>
  <div class="in-full-screen-loading">
    <InLoading />
  </div>
</template>

<script>
import InLoading from './InLoading.vue'
export default {
  name: 'InFullScreenLoading',

  components: {
    InLoading
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.in-full-screen-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // background-color: #000;
}
</style>
