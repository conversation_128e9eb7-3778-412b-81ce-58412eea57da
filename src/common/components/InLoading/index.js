import Vue from 'vue'
import loading from './InLoading.vue'

let instance

export const LoadingConstructor = Vue.extend(loading)

const initInstance = () => {
  instance = new LoadingConstructor({
    el: document.createElement('div')
  })
  document.body.appendChild(instance.$el)
}

const InLoading = function (option) {
  if (!instance) {
    initInstance()
  }
  instance.message = option.message

  Vue.nextTick(() => {
    instance.show()
  })
}

InLoading.show = (message) => {
  InLoading({
    message
  })
}

InLoading.close = () => {
  Vue.nextTick(() => {
    if (instance) {
      instance.close()
    }
  })
}

export { InLoading }
