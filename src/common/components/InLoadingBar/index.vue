<template>
  <div class="in-loading-bar">
    <div class="in-loading-bar__container">
      <div
        class="in-loading-bar__tooltip"
        :style="{
          marginLeft: `${tooltipRatio * 100}%`
        }"
      >
        {{ (value * 100).toFixed(0) }}%
      </div>
      <div class="in-loading-bar__track">
        <div class="in-loading-bar__progress" :style="{ width: `${progressRatio * 100}%` }"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { rangeMap } from '@/common/utils'

export default {
  name: 'InLoadingBar',

  components: {},

  props: {
    value: {} // 0-1
  },

  data() {
    return {}
  },

  computed: {
    progressRatio() {
      return this.value
    },

    tooltipRatio() {
      return rangeMap(this.value, 0, 1, -0.04, 0.94)
    }
  },

  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.in-loading-bar {
  &__container {
    width: 100%;
    border-radius: 8px;
    position: relative;
  }
  &__track {
    position: relative;
    background: #f1efe7;
    border-radius: 4px;
    width: inherit;
    height: 5px;
  }
  &__progress {
    // width: 40%;
    left: 0%;
    height: 100%;
    background: linear-gradient(90deg, #ffe457 20%, #ffc52c 99%);
    border-radius: 8px;
    will-change: width, left;
    transition: width 0.28s, left 0.28s, right 0.28s, height 0.28s, top 0.28s, bottom 0.28s;
  }
  &__tooltip {
    transition: all 0.28s;
    will-change: left;
    // margin-left: 0;
    background: url('./assets/tooltip.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 36px;
    // padding-bottom: 2px;
    height: 24px;
    line-height: 22px;
    font-family: OpenSans-SemiBold, OpenSans;
    font-size: 12px;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    margin-bottom: 9px;
  }
}
</style>
