<template>
  <button
    :class="[
      'in-button',
      { 'in-button--small': small },
      { 'in-button--disabled': disable },
      `in-button--${color}`
    ]"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot></slot>
  </button>
</template>

<script>
import { stopAndPrevent } from '@/common/components/InForm/utils/event'
export default {
  name: 'InButton',

  components: {},

  props: {
    small: {
      type: Boolean,
      default: false
    },
    disable: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: 'primary' // normal grey
    }
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {
    handleClick(e) {
      if (this.disable) {
        stopAndPrevent(e)
        return
      }
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.in-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 46px;
  border-radius: 26px;
  font-size: 16px;
  font-family: OpenSans-SemiBold, OpenSans;
  line-height: 22px;
  outline: none;

  &:focus {
    outline: none;
  }

  &--small {
    width: 248px;
    height: 40px;
  }

  &--disabled {
    border: none !important;
    background: #d3d3d3 !important;
    color: #ffffff !important;
  }

  &--primary {
    background: var(--main-color, #00903b);
    color: #ffffff;
  }

  &--normal {
    color: var(--main-color, #00903b);
    background: #fff;
    border: 1px solid var(--main-color, #00903b);
  }

  &--grey {
    background: #dddddd;
    color: #fff;
  }

  &--grey-outline {
    border: 1px solid #ccd1d9;
    background: #fff;
    color: #666666;
  }

  &--prime {
    background: #000;
    color: #ffd4ad;
  }

  &--normal1 {
    color: #125a3d;
    background: #fff;
  }
}
</style>
