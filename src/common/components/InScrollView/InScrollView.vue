<template>
  <div ref="container" class="in-scroll-view">
    <div class="in-scroll-view__content">
      <div v-if="refresh" class="in-scroll-view__refresh">
        <img v-show="beforePullDown && !hiddenArrow" :class="['arrow-down', { rotate: !inPullingDownThreshold }]"
          :src="require('./assets/arrow_down.png')" />
        <Spinner v-show="!beforePullDown" class="spinner" />
      </div>
      <slot></slot>
      <div v-if="loadMore && isEnableLoadMore && beforePullDown" class="in-scroll-view__loading">
        <template v-if="!loadEnd">
          <span v-show="!isPullingUp" class="pullup-txt">
            <!-- Pull up and load more -->
            <!-- <Spinner class="spinner" /> -->
            <div style="height: 20px"></div>
          </span>
          <Spinner v-show="isPullingUp" class="spinner" />
        </template>
        <span v-else class="pullup-txt">
          <div style="height: 20px"></div>
          <!-- No more data -->
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import BScroll from '@better-scroll/core'
import Pulldown from '@better-scroll/pull-down'
import Pullup from '@better-scroll/pull-up'
import Spinner from '../InLoading/Spinner.vue'

BScroll.use(Pulldown)
BScroll.use(Pullup)

export default {
  name: 'InScrollView',

  components: {
    Spinner
  },

  props: {
    refresh: {
      type: Boolean,
      default: false
    },
    immediateRefresh: {
      type: Boolean,
      default: false
    },
    loadMore: {
      type: Boolean,
      default: false
    },
    hiddenArrow: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      scroll: null,
      beforePullDown: true,
      isPullingDown: false,
      isPullingUp: false,
      inPullingDownThreshold: true,
      loadEnd: false,
      isEnableLoadMore: true
    }
  },

  computed: {},

  created() { },

  mounted() {
    console.log('init bscroll')
    this.scroll = new BScroll(this.$refs.container, {
      eventPassthrough: 'horizontal',
      click: true,
      // preventDefault: true,
      bounceTime: 300,
      pullDownRefresh: this.refresh
        ? {
          threshold: 40
          // stop: 30,
        }
        : false,
      pullUpLoad: this.loadMore
        ? {
          threshold: 30
        }
        : false
    })
    if (this.refresh) {
      this.scroll.on('pullingDown', this.onPullingDown)
      this.scroll.on('enterThreshold', this.onPullingDownEnterThreshold)
      this.scroll.on('leaveThreshold', this.onPullingDownLeaveThreshold)
      this.scroll.on('scroll', this.onScroll)
    }
    if (this.loadMore) {
      this.scroll.on('pullingUp', this.onPullingUp)
    }

    if (this.immediateRefresh) {
      this.scroll.autoPullDownRefresh()
    }
    setTimeout(() => {
      this.scroll.refresh()
    }, 300)
  },

  methods: {
    onPullingDown() {
      this.beforePullDown = false
      this.isPullingDown = true
      this.$emit('pulling-down', this.handleFinishPullDown)
    },

    onPullingDownEnterThreshold() {
      this.inPullingDownThreshold = true
    },

    onPullingDownLeaveThreshold() {
      this.inPullingDownThreshold = false
    },

    handleFinishPullDown() {
      this.isPullingDown = false
      this.scroll.finishPullDown()
      setTimeout(() => {
        this.beforePullDown = true
        this.inPullingDownThreshold = true
        this.scroll.refresh()
      }, 400)
    },

    onPullingUp() {
      this.isPullingUp = true
      this.$emit('pulling-up', this.handleFinishPullUp)
    },
    onScroll() {
      this.$emit('scroll', this.scroll.y)
    },

    handleFinishPullUp() {
      this.scroll.finishPullUp()
      setTimeout(() => {
        console.log('done')
        this.scroll.refresh()
        this.isPullingUp = false
      }, 400)
    },

    openPullUp(config = {}) {
      this.scroll.openPullUp(config)
    },

    closePullUp() {
      this.scroll.closePullUp()
    },

    closePullDown() {
      this.scroll.closePullDown()
    },

    disableLoadMore() {
      this.isEnableLoadMore = false
      this.closePullUp()
    },

    disableRefresh() {
      this.closePullDown()
    },

    enableLoadMore(config = {}) {
      this.isEnableLoadMore = true
      this.loadEnd = false
      this.openPullUp(config)
    },

    loadMoreEnd() {
      this.loadEnd = true
      this.closePullUp()
    },

    refreshScrollView() {
      this.scroll.refresh()
    }
  }
}
</script>

<style lang="scss" scoped>
.in-scroll-view {
  .spinner {
    height: 20px;
    color: #00903b;
  }

  /* stylelint-disable-next-line block-no-empty */
  &__content {}

  &__refresh {
    position: absolute;
    transform: translateY(-100%) translateZ(0);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding-bottom: 10px;
  }

  &__loading {
    // transform: translateY(-100%) translateZ(0);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding-bottom: 10px;
    font-size: 12px;
    color: #6f6f6f;
  }

  .arrow-down {
    width: 16px;
    height: 16px;
    transition: transform 0.3s;

    &.rotate {
      transform: rotate(180deg);
    }
  }
}
</style>
