<template>
  <InPopup ref="dialog" @hide="handleHide">
    <div class="questionnaire">
      <img class="questionnaire__bg" :src="require('./assets/header.png')" />
      <div class="questionnaire__header">
        <p class="questionnaire__title">
          Hai, <br />
          isi kuisioner yuk
        </p>
        <p class="questionnaire__desc">
          Untuk melayani Anda lebih baik lagi, mohon berikan alasan Anda meninggalkan AdaKami
        </p>
        <p v-if="survey.maxSelected > 0 && survey.form" class="questionnaire__tip">
          (Pilih maks {{ survey.maxSelected }} item)
        </p>
      </div>
      <div class="questionnaire__content">
        <InCheckboxGroup v-model="checkedList" :max="survey.maxSelected" :single="!survey.form">
          <InCheckbox
            v-for="item in surveyQuestions"
            :key="item.questionId"
            class="check-item"
            :value="item.questionId"
            >{{ item.question }}</InCheckbox
          >
          <InCheckbox v-if="survey.otherOption" class="check-item" value="-1"
            >Lainnya
            <InInput v-model="otherReason" class="other-input" @focus="onInputFocus" />
          </InCheckbox>
        </InCheckboxGroup>
      </div>
      <div class="questionnaire__footer">
        <InButton color="grey-outline" class="cancel-btn" @click="hide">Tidak mau isi</InButton>
        <InButton :disable="survey.emptySubmit ? false : checkedList.length === 0" @click="handleConfirm">
          Kirim
        </InButton>
      </div>
    </div>
  </InPopup>
</template>

<script>
import { InPopup } from '@/common/components/InDialog'
import { InButton } from '@/common/components/InButton'
import { InCheckbox, InCheckboxGroup } from '@/common/components/InForm/InCheckbox'
import { InInput } from '@/common/components/InForm/InInput'

export default {
  name: 'PopupLeaveQuestionnaire',

  components: {
    InPopup,
    InButton,
    InCheckboxGroup,
    InCheckbox,
    InInput
  },

  props: {
    survey: {}
  },

  data() {
    return {
      checkedList: [],
      otherReason: ''
    }
  },

  computed: {
    surveyQuestions({ survey }) {
      return survey?.surveyQuestions ?? []
    }
  },

  watch: {
    checkedList(val) {
      if (val.indexOf('-1') < 0) {
        this.otherReason = ''
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    show() {
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$emit('hide')
    },

    handleConfirm() {
      this.$emit('ok', {
        questionIds: this.checkedList.filter((m) => m !== '-1'),
        other: this.checkedList.indexOf('-1') > -1 ? this.otherReason : null
      })
    },

    onInputFocus() {
      if (this.checkedList.length >= this.survey.maxSelected) return
      const i = this.checkedList.indexOf('-1')
      if (i < 0) {
        this.checkedList.push('-1')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.questionnaire {
  width: 100%;
  max-height: 85%;
  position: absolute;
  bottom: 0;
  left: 0;
  background: #fff;
  border-radius: 14px 14px 0px 0px;
  box-sizing: border-box;
  z-index: 0;
  padding: 24px 20px 20px 20px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20px);
  display: flex;
  flex-direction: column;
  &__bg {
    position: absolute;
    width: 100%;
    object-fit: cover;
    top: -55px;
    left: 0;
    z-index: -1;
  }
  &__header {
  }
  &__title {
    font-size: 20px;
    font-family: OpenSans-SemiBold, OpenSans;
    color: #00903b;
    line-height: 33px;
  }
  &__desc {
    margin-top: 16px;
    font-size: 16px;
    font-family: OpenSans-Semibold, OpenSans;
    color: #333333;
    line-height: 20px;
  }
  &__tip {
    margin-top: 8px;
    font-size: 12px;
    font-family: OpenSans-Regular, OpenSans;
    color: #646464;
    line-height: 17px;
  }
  &__content {
    margin-top: 22px;
    max-height: 220px;
    overflow: auto;
  }

  &__footer {
    display: flex;
    margin-top: 20px;
    .cancel-btn {
      margin-right: 12px;
    }
  }
}

.check-item {
  padding: 12px 0;
}
.other-input {
  margin-top: 10px;
}
</style>
