<template>
  <InPopup ref="dialog" class="in-common-popup" :disable-mask-close="disableMaskClose" @hide="handleHide">
    <div class="in-common-popup__wrap">
      <div class="in-common-popup__header">
        <div class="title">{{ title }}</div>
        <img :src="require('./assets/mini-close.png')" class="close-img" @click="handleHide" />
      </div>
      <slot></slot>
    </div>
  </InPopup>
</template>

<script>
import InPopup from './InPopup.vue'

export default {
  name: 'InCommonPopup',

  components: {
    InPopup
  },

  props: {
    title: {},
    disableMaskClose: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    show() {
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$refs.dialog.visible = false
      setTimeout(() => {
        this.$emit('hide')
      }, 300)
    }
  }
}
</script>

<style lang="scss" scoped>
.in-common-popup {
  &__wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    height: auto;
    background: #ffffff;
    border-radius: 14px 14px 0px 0px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 16px;
    padding-bottom: env(safe-area-inset-bottom);
  }
  &__header {
    display: flex;
    align-items: center;
    .title {
      font-size: 16px;
      font-family: OpenSans-SemiBold, OpenSans;
      color: #333333;
      line-height: 20px;
      text-align: center;
      flex: 1;
    }
    .close-img {
      width: 16px;
      height: 16px;
      margin-right: 20px;
    }
  }
}
</style>
