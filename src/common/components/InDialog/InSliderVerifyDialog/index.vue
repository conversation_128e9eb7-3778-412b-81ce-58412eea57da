<template>
  <InDialog ref="dialog" class="in-slider-dialog" click-mask-close @hide="handleHide">
    <div ref="wrap" class="in-slider-dialog__wrap">
      <div class="panel-header">
        <div class="title">Verifikasi capcha</div>
        <img src="./assets/grey_refresh.png" @click="doRefresh" />
      </div>
      <div id="imgContainer" class="img-container">
        <img id="parentImg" class="parent-img" :style="parentImageStyle" />
        <img
          v-if="calibrationInfo.slidingImage"
          ref="sliderImg"
          class="slider-img"
          :src="'data:image/jpg;base64,' + calibrationInfo.slidingImage"
          :style="sliderImageStyle"
        />
      </div>
      <div ref="dragDiv" class="drag">
        <div ref="dragPastBg" class="drag-past-bg"></div>
        <div class="drag-text" :class="{ 'drag-text-success': confirmSuccess }">
          Geser untuk memverifikasi
        </div>
        <div
          ref="handler"
          :class="{ 'handler-ok-bg': confirmSuccess }"
          class="handler handler_bg"
          style="position: absolute; top: 0px; left: 0px"
          @touchstart="mousedownFn($event)"
          @mousedown="mousedownFn($event)"
        ></div>
      </div>
    </div>
  </InDialog>
</template>

<script>
import InDialog from '../InDialog.vue'
import { InToast } from '@/common/components/InToast'

export default {
  name: 'InSliderVerifyDialog',

  components: {
    InDialog
  },

  props: {
    api: {},
    mobile: {}
  },

  data() {
    return {
      calibrationInfo: {},
      verificationToken: {},
      beginClientX: 0,
      mouseMoveStata: false,
      maxwidth: '',
      confirmSuccess: false,
      sliderImageScale: 1,
      sliderResultX: 0
    }
  },

  computed: {
    parentImageStyle() {
      return 'background-image: url(data:image/jpg;base64,' + this.calibrationInfo.originalImage + ')'
    },

    sliderImageStyle() {
      let translateValue = (-(1 - this.sliderImageScale) / 2) * 100
      return {
        top: this.calibrationInfo.targetYHeightPercent * 100 + '%',
        transform:
          'translate(' +
          translateValue +
          '%, ' +
          translateValue +
          '%) scale(' +
          this.sliderImageScale +
          ', ' +
          this.sliderImageScale +
          ')'
      }
    }
  },

  created() {},

  mounted() {
    const el = document.querySelector('.in-slider-dialog .in-dialog__main')
    // 桌面端
    el.addEventListener('mousemove', this.mouseMoveFn)
    el.addEventListener('mouseup', this.moseUpFn)
    // 移动端
    el.addEventListener('touchmove', this.mouseMoveFn)
    el.addEventListener('touchend', this.moseUpFn)

    window.onresize = () => {
      this.updateDisplay()
    }
  },

  beforeUnmount() {
    const el = document.querySelector('.in-slider-dialog .in-dialog__main')
    // 桌面端
    el.removeEventListener('mousemove', this.mouseMoveFn)
    el.removeEventListener('mouseup', this.moseUpFn)
    // 移动端
    el.removeEventListener('touchmove', this.mouseMoveFn)
    el.removeEventListener('touchend', this.moseUpFn)
    window.onresize = null
  },

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    async show() {
      await this.queryImageVerificationCode()
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$refs.dialog.visible = false
      setTimeout(() => {
        this.$emit('hide')
      }, 300)
    },

    updateDisplay() {
      this.maxwidth = this.$refs.dragDiv.clientWidth - this.$refs.handler.clientWidth
      this.sliderImageScale = this.$refs.dragDiv.clientWidth / this.calibrationInfo.targetXWidthOri
      this.slideToOffset(0)
    },

    mousedownFn(e) {
      if (!this.confirmSuccess) {
        e.preventDefault && e.preventDefault() // 阻止文字选中等 浏览器默认事件
        this.mouseMoveStata = true
        let touch = e.changedTouches ? e.changedTouches[0] : e
        this.beginClientX = touch.clientX
      }
    },

    mouseMoveFn(e) {
      if (this.mouseMoveStata) {
        let touch = e.changedTouches ? e.changedTouches[0] : e
        let width = touch.clientX - this.beginClientX
        this.slideToOffset(Math.min(Math.max(0, width), this.maxwidth))
        this.sliderResultX = width / this.$refs.dragDiv.clientWidth
      }
    },

    moseUpFn(e) {
      if (this.mouseMoveStata) {
        this.mouseMoveStata = false
        let touch = e.changedTouches ? e.changedTouches[0] : e
        var width = touch.clientX - this.beginClientX
        if (width > 0 && width < this.maxwidth) {
          this.$emit('ok', {
            sliderResultX: this.sliderResultX,
            token: this.verificationToken,
            reset: () => {
              this.doRefresh(0)
            }
          })
        } else {
          InToast.info('Verifikasi keamanan gagal')
          this.slideToOffset(0)
        }
      }
    },
    slideToOffset(offsetX) {
      this.$refs.sliderImg.style.left = offsetX + 'px'
      this.$refs.handler.style.left = offsetX + 'px'
      this.$refs.dragPastBg.style.width = offsetX + 'px'
    },

    doRefresh() {
      this.slideToOffset(0)
      this.queryImageVerificationCode()
    },

    async queryImageVerificationCode() {
      try {
        const { content } = await this.api({
          verifyToken: this.mobile
        })
        this.calibrationInfo = content
        this.verificationToken = content.verificationToken || ''
      } finally {
        setTimeout(() => {
          this.updateDisplay()
        }, 100)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.in-slider-dialog {
  &__wrap {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    padding: 15px 3vw;
    box-sizing: border-box;
    border-radius: 4px;
    background-color: white;
  }
  .panel-header {
    position: relative;
    margin-bottom: 15px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    img {
      width: 16px;
    }
    .title {
      flex-grow: 1;
      text-align: center;
    }
  }
  .img-container {
    position: relative;
    margin-bottom: 10px;
    .parent-img {
      display: block;
      width: 84vw;
      height: 42vw;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
    .slider-img {
      display: block;
      position: absolute;
      left: 0;
    }
  }
  .drag {
    position: relative;
    background-color: #e8e8e8;
    width: 100%;
    height: 34px;
    line-height: 34px;
    text-align: center;
    .handler {
      width: 40px;
      height: 32px;
      border: 1px solid #ccc;
      cursor: move;
    }
    .handler_bg {
      background: #fff
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTEyNTVEMURGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTEyNTVEMUNGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2MTc5NzNmZS02OTQxLTQyOTYtYTIwNi02NDI2YTNkOWU5YmUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+YiRG4AAAALFJREFUeNpi/P//PwMlgImBQkA9A+bOnfsIiBOxKcInh+yCaCDuByoswaIOpxwjciACFegBqZ1AvBSIS5OTk/8TkmNEjwWgQiUgtQuIjwAxUF3yX3xyGIEIFLwHpKyAWB+I1xGSwxULIGf9A7mQkBwTlhBXAFLHgPgqEAcTkmNCU6AL9d8WII4HOvk3ITkWJAXWUMlOoGQHmsE45ViQ2KuBuASoYC4Wf+OUYxz6mQkgwAAN9mIrUReCXgAAAABJRU5ErkJggg==')
        no-repeat center;
    }
    .handler-ok-bg {
      background: #fff
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDlBRDI3NjVGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDlBRDI3NjRGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDphNWEzMWNhMC1hYmViLTQxNWEtYTEwZS04Y2U5NzRlN2Q4YTEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+k+sHwwAAASZJREFUeNpi/P//PwMyKD8uZw+kUoDYEYgloMIvgHg/EM/ptHx0EFk9I8wAoEZ+IDUPiIMY8IN1QJwENOgj3ACo5gNAbMBAHLgAxA4gQ5igAnNJ0MwAVTsX7IKyY7L2UNuJAf+AmAmJ78AEDTBiwGYg5gbifCSxFCZoaBMCy4A4GOjnH0D6DpK4IxNSVIHAfSDOAeLraJrjgJp/AwPbHMhejiQnwYRmUzNQ4VQgDQqXK0ia/0I17wJiPmQNTNBEAgMlQIWiQA2vgWw7QppBekGxsAjIiEUSBNnsBDWEAY9mEFgMMgBk00E0iZtA7AHEctDQ58MRuA6wlLgGFMoMpIG1QFeGwAIxGZo8GUhIysmwQGSAZgwHaEZhICIzOaBkJkqyM0CAAQDGx279Jf50AAAAAABJRU5ErkJggg==')
        no-repeat center;
    }
    .drag-past-bg {
      background-color: #7ac23c;
      height: 34px;
      width: 0px;
    }
    .drag-text {
      position: absolute;
      top: 0px;
      width: 100%;
      text-align: center;
      -moz-user-select: none;
      -webkit-user-select: none;
      user-select: none;
      -o-user-select: none;
      -ms-user-select: none;
      background: linear-gradient(to right, black, #eee, black);
      -webkit-background-clip: text;
      color: transparent;
      animation: flowAnimation 3s linear infinite;
      background-size: 400% 100%;
    }
    @keyframes flowAnimation {
      0% {
        background-position: 100% 0;
      }
      100% {
        background-position: 0 0;
      }
    }

    .drag-text-success {
      color: white;
    }
  }
}
</style>
