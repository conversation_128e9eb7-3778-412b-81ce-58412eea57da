import globalDialog from './global-dialog'
import MessageBox from './InMessageBox.vue'
import InOTPVerifyDialog from '@/common/components/InDialog/InOTPVerifyDialog/index.vue'
const createDialog = globalDialog()

const defaultOptions = {
  title: 'Pemberitahuan',
  content: '',
  imgUrl: '',
  imgSize: 'small',
  type: '',
  confirmButtonText: 'Oke',
  cancelButtonText: '',
  buttonsLayout: 'horizontal'
}

/**
 * @description: 打开message-box
 * @param {*} options
 * @param {string} options.title
 * @param {string} options.content
 * @param {string} options.imgUrl
 * @param {big|small} options.imgSize
 * @param {string} options.confirmButtonText
 * @param {string} options.cancelButtonText
 * @param {vertical|horizontal} options.buttonsLayout
 * @param {string} options.type
 * @return {*}
 */
const createMessageBox = function (options = {}) {
  // const { title, content, imgUrl, imgSize, type, confirmButtonText, cancelButtonText, buttonsLayout } = options;

  return createDialog({
    component: MessageBox,
    ...Object.assign({}, defaultOptions, options)
  })
}

const createInOtpBox = function (options = {}) {
  // const { title, content, imgUrl, imgSize, type, confirmButtonText, cancelButtonText, buttonsLayout } = options;

  return createDialog({
    component: InOTPVerifyDialog,
    ...Object.assign({}, options)
  })
}

export { createMessageBox, createInOtpBox }
