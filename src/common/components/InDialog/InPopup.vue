<template>
  <div class="in-popup">
    <transition name="in-transition--fade">
      <div v-show="visible" class="in-popup__mask"></div>
    </transition>
    <transition :name="visible ? 'in-transition--slide-up' : 'in-transition--slide-down'">
      <div v-show="visible" ref="popBaseMain" class="in-popup__main">
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'InPopup',
  components: {},

  props: {
    disableMaskClose: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      visible: false
    }
  },
  computed: {},

  watch: {},
  mounted() {
    document.addEventListener('click', this.clickOutside)

    document.getElementsByClassName('in-popup')[0].style.height = window.innerHeight + 'px'
    console.log(window.innerHeight)
    window.onresize = () => {
      console.log(window.innerHeight)
      document.getElementsByClassName('in-popup')[0].style.height = window.innerHeight + 'px'
    }
  },
  // eslint-disable-next-line vue/no-deprecated-destroyed-lifecycle
  beforeDestroy() {
    document.removeEventListener('click', this.clickOutside)
    window.onresize = null
  },

  created() {},

  methods: {
    show() {
      this.visible = true
      this.$emit('show')
    },

    hide() {
      this.visible = false
      setTimeout(() => {
        this.$emit('hide')
      }, 300)
    },
    clickOutside(event) {
      if (this.disableMaskClose) {
        return
      }

      if (this.visible && event.target === this.$refs.popBaseMain) {
        this.hide()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/styles/transition.scss';

.in-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // height: 100vh;

  // height: -webkit-fill-available;
  width: 100%;
  z-index: 1001;
  transform: translateZ(1px);
  &__mask {
    background: rgba(0, 0, 0, 0.7);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    z-index: -1;
    pointer-events: all;
  }
  &__main {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
  }
}
</style>
