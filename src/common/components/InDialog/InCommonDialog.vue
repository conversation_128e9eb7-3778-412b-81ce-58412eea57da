<template>
  <InDialog ref="dialog" class="in-common-dialog" @hide="handleHide">
    <div class="in-common-dialog__wrap">
      <slot></slot>
      <img class="img-close" :src="require('./assets/close.png')" @click="handleHide" />
    </div>
  </InDialog>
</template>

<script>
import InDialog from './InDialog.vue'

export default {
  name: 'InCommonDialog',

  components: {
    InDialog
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    show() {
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$refs.dialog.visible = false
      setTimeout(() => {
        this.$emit('hide')
      }, 300)
    }
  }
}
</script>

<style lang="scss" scoped>
.in-common-dialog {
  min-height: 100vh;
  position: absolute !important;
  width: 100%;
  ::v-deep .in-dialog__mask {
    height: 100vh;
    width: 100%;
    position: absolute !important;
  }

  &__wrap {
    width: 100%;
  }
}
.img-close {
  display: block;
  margin: 0 auto;
  margin-top: 26px;
  height: 38px;
  width: 38px;
}
</style>
