<template>
  <InDialog ref="dialog" class="in-message-box" @hide="handleHide">
    <div class="in-message-box__wrap">
      <img
        v-if="showClose"
        class="mini-close"
        :src="require('./assets/mini-close.png')"
        @click="handleCancelClick"
      />
      <img v-if="imgSize === 'big' && imgUrl" class="in-message-box__img big" :src="imgUrl" />
      <div class="in-message-box__main">
        <img v-if="imgSize === 'small' && imgUrl" class="in-message-box__img small" :src="imgUrl" />
        <p :class="['in-message-box__title', { center: !!imgUrl }]">{{ title }}</p>
        <div class="in-message-box__content" v-html="content"></div>
        <div :class="['in-message-box__buttons', buttonsLayout]">
          <InButton v-if="confirmButtonText" class="in-message-box__button" @click="handleConfirmClick">
            {{ confirmButtonText }}
          </InButton>
          <InButton
            v-if="cancelButtonText"
            class="in-message-box__button"
            color="grey-outline"
            @click="handleCancelClick"
          >
            {{ cancelButtonText }}
          </InButton>
        </div>
      </div>
    </div>
  </InDialog>
</template>

<script>
import InDialog from './InDialog.vue'
import { InButton } from '../InButton'

export default {
  name: 'InMessageBox',

  components: {
    InDialog,
    InButton
  },

  props: {
    title: {},
    content: {},
    buttonsLayout: {},
    confirmButtonText: {},
    cancelButtonText: {},
    type: {},
    imgUrl: {},
    imgSize: {},
    showClose: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    show() {
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$emit('hide')
    },

    handleConfirmClick() {
      this.$emit('ok')
    },

    handleCancelClick() {
      this.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.in-message-box {
  &__wrap {
    width: 300px;
    background: #ffffff;
    border-radius: 14px;
    // padding: 24px;
    box-sizing: border-box;
    position: relative;
  }
  &__img {
    display: block;

    &.big {
      width: 300px;
      height: auto;
    }
    &.small {
      width: 90px;
      height: auto;
      margin: 0 auto;
      margin-bottom: 8px;
    }
  }
  &__main {
    padding: 24px;
    box-sizing: border-box;
  }
  &__title {
    font-size: 20px;
    font-family: OpenSans-SemiBold, OpenSans;
    color: #333333;
    line-height: 28px;
    margin-bottom: 12px;
    &.center {
      text-align: center;
    }
  }
  &__content {
    font-size: 14px;
    font-family: OpenSans-Regular, OpenSans;
    color: #646464;
    line-height: 20px;
  }
  &__buttons {
    margin-top: 20px;
    &.vertical {
    }
    &.horizontal {
      display: flex;
      flex-direction: row-reverse;
    }
  }
  &__button {
    .vertical & {
      &:last-child {
        margin-top: 12px;
      }
    }
    .horizontal & {
      &:first-child {
        margin-left: 8px;
      }
    }
  }
}

.mini-close {
  display: block;
  width: 16px;
  height: 16px;
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
