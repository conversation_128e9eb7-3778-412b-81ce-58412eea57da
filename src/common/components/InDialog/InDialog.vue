<template>
  <div class="in-dialog">
    <transition name="q-transition--fade">
      <div v-show="visible" class="in-dialog__mask"></div>
    </transition>
    <transition name="q-transition--scale">
      <div v-show="visible" class="in-dialog__main" @click.self="maskClick">
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'InDialog',

  components: {},

  props: {
    clickMaskClose: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      visible: false,
      transitionState: false
    }
  },

  computed: {},

  watch: {},

  created() {},

  methods: {
    show() {
      this.visible = true
      this.$emit('show')
    },

    hide() {
      this.visible = false
      setTimeout(() => {
        this.$emit('hide')
      }, 300)
    },

    maskClick() {
      console.log('maskClick')
      if (this.clickMaskClose) {
        this.hide()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.in-dialog {
  position: fixed;
  height: 100vh;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  transform: translateZ(1px);
  &__mask {
    background: rgba(0, 0, 0, 0.7);
    position: absolute;
    height: 100vh;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    pointer-events: all;
  }
  &__main {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

$transition-easing: cubic-bezier(0.215, 0.61, 0.355, 1); // easeOutCubic

.q-transition {
  &--fade,
  &--scale {
    &-leave-active {
      position: absolute;
    }
  }

  &--fade {
    &-enter-active,
    &-leave-active {
      transition: opacity 0.3s ease-out;
    }
    &-enter,
    &-leave,
    &-leave-to {
      opacity: 0;
    }
  }

  &--scale {
    &-enter-active,
    &-leave-active {
      transition: opacity 0.3s, transform 0.3s $transition-easing;
    }
    &-enter,
    &-leave,
    &-leave-to {
      opacity: 0;
      transform: scale3d(0, 0, 1);
    }
  }
}
</style>
