/* eslint-disable no-unused-vars */
import Vue from 'vue'
import { prevent } from '../InForm/utils/event'

export function merge(target, source) {
  for (const key in source) {
    if (key !== 'spinner' && Object(source[key]) === source[key]) {
      target[key] = Object(target[key]) !== target[key] ? {} : { ...target[key] }

      merge(target[key], source[key])
    } else {
      target[key] = source[key]
    }
  }
}

export default function (DefaultComponent) {
  return ({ className, class: klass, style, component, root, parent, ...props }) => {
    klass !== void 0 && (props.cardClass = klass)
    style !== void 0 && (props.cardStyle = style)

    const isCustom = component !== void 0
    let DialogComponent, attrs

    if (isCustom === true) {
      DialogComponent = component
    } else {
      DialogComponent = DefaultComponent
      attrs = props
    }

    const okFns = [],
      cancelFns = [],
      API = {
        onOk(fn) {
          okFns.push(fn)
          return API
        },
        onCancel(fn) {
          cancelFns.push(fn)
          return API
        },
        onDismiss(fn) {
          okFns.push(fn)
          cancelFns.push(fn)
          return API
        },
        hide() {
          vm.$refs.dialog.hide()
          return API
        },
        update({ className, class: klass, style, component, root, parent, ...cfg }) {
          if (vm !== null) {
            klass !== void 0 && (cfg.cardClass = klass)
            style !== void 0 && (cfg.cardStyle = style)

            if (isCustom === true) {
              Object.assign(props, cfg)
            } else {
              merge(props, cfg)

              // need to change "attrs" reference to
              // actually reflect it in underlying component
              // when we force update it
              attrs = { ...props }
            }

            vm.$forceUpdate()
          }

          return API
        }
      }

    const node = document.createElement('div')
    document.body.appendChild(node)

    let emittedOK = false

    const on = {
      ok: (data) => {
        emittedOK = true
        if (okFns.length === 0) {
          vm.hide()
        } else {
          okFns.forEach((fn) => {
            fn(data, vm.hide)
          })
        }
      },

      hide: () => {
        vm.$destroy()
        vm.$el.remove()
        vm = null

        if (emittedOK !== true) {
          cancelFns.forEach((fn) => {
            fn()
          })
        }
      }
    }

    let vm = new Vue({
      el: node,
      name: 'QGlobalDialog',
      parent: parent === void 0 ? root : parent,
      data() {
        return {
          originOverflow: ''
        }
      },
      mounted() {
        if (this.$refs.dialog !== void 0) {
          this.$refs.dialog.show()
        } else {
          on['hook:mounted'] = () => {
            this.$refs.dialog !== void 0 && this.$refs.dialog.show()
          }
        }

        this.stopScroll()
      },
      // eslint-disable-next-line vue/no-deprecated-destroyed-lifecycle
      destroyed() {
        this.canScroll()
      },

      methods: {
        hide() {
          this.canScroll()
          this.$refs.dialog.hide()
        },
        stopScroll() {
          this.originOverflow = document.body.style.overflow
          document.body.style.overflow = 'hidden'
          document.addEventListener('touchmove', this.preventScroll, true) //禁止页面滑动
        },
        preventScroll(e) {
          e.preventDefault()
        },

        //取消滑动限制
        canScroll() {
          document.body.style.overflow = this.originOverflow //出现滚动条
          document.removeEventListener('touchmove', this.preventScroll, true)
        }
      },

      render(h) {
        return h(DialogComponent, {
          ref: 'dialog',
          props,
          attrs,
          on
        })
      }
    })

    return API
  }
}
