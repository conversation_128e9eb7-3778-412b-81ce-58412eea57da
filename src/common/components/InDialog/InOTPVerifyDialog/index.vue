<template>
  <InDialog ref="dialog" class="in-otp-dialog" @hide="handleHide">
    <div class="in-otp-dialog__wrap">
      <img v-if="isShowCloseIcon" class="icon-close" :src="require('./assets/close.png')" @click="handleHide" />

      <p class="in-otp-dialog__tip">{{ title }}</p>
      <p class="in-otp-dialog__number">{{ phoneMsg }}</p>
      <div class="in-otp-dialog__main">
        <div class="number-list">
          <div v-for="index in length" :key="index" class="number-item">
            {{ text.length + 1 > index ? text.substring(index - 1, index) : '' }}
            <div v-if="index - 1 == text.length && editing" class="cursor"></div>
          </div>
        </div>
        <div class="text-field-cover" @click="onCoverClick"></div>
        <input ref="input" v-model="text" class="text-field" type="tel" :disabled="disable" :maxLength="length"
          @focus="onFocus" @blur="handleOnBlur" @change="onChange" @input="onInput" @keyup.enter="onReturn" />
      </div>
      <div class="otp-info">
        <div class="otp-info__left">
          <!-- <img class="icon-mobile" :src="require('./assets/mobile.png')" />
          Kode verifikasi suara -->
        </div>
        <div class="otp-info__right">
          <span v-if="cooldown" class="countdown">{{ time.ss }}s</span>
          <span v-else class="text-button" @click="handleSendClick">Kirim ulang</span>
        </div>
      </div>
      <InButton v-if="isShowCloseIcon" class="submit-button" :disable="text.length < 6" @click="handleConfirmClick">
        Kirim
      </InButton>
      <div v-else class="row-button">
        <InButton color="grey-outline" @click="handleHide">Keluar</InButton>
        <InButton color="primary" @click="handleConfirmClick" :disable="text.length < 6">Bayar</InButton>
      </div>
    </div>
  </InDialog>
</template>

<script>
import InDialog from '../InDialog.vue'
import { InButton } from '@/common/components/InButton'
import { Countdown } from '@/common/utils'
import { formatPhoneNumberMask } from '@/common/utils/format'

export default {
  name: 'InOTPVerifyDialog',

  components: {
    InDialog,
    InButton
  },

  props: {
    mobile: {},
    length: {},
    disable: {
      type: Boolean,
      default: false
    },
    onBlur: {},
    title: {
      type: String,
      default: 'Kode verifikasi dikirim'
    },
    isShowCloseIcon: {
      type: Boolean,
      default: true
    },
    mobilePrefix: {
      type: String,
      default: ''
    },
    isMaskPhone: {
      type: Boolean,
      default: false
    },

  },

  data() {
    return {
      text: '',
      editing: false,
      focusTimeStamp: 0,
      cooldown: true,
      countdown: null,
      time: {
        ss: '59'
      }
    }
  },

  computed: {
    phoneMsg: function () {
      let res = ''
      if (this.mobilePrefix.length !== 0) {
        res = this.mobilePrefix + ': '
      }
      if (this.isMaskPhone) {
        res = res + formatPhoneNumberMask(this.mobile, 4, 2)
      }
      return res
    }
  },

  created() { },

  mounted() {
    this.startCountdown()
    this.$nextTick(() => {
      this.$refs.input.focus()
    })
  },

  beforeUnmount() {
    this.countdown?.clear()
  },

  methods: {
    // 以下方法是必需的
    // (不要改变它的名称 --> "show")
    async show() {
      this.$refs.dialog.show()
    },

    // 以下方法是必需的
    // (不要改变它的名称 --> "hide")
    hide() {
      this.$refs.dialog.hide()
    },

    handleHide() {
      this.$refs.dialog.visible = false
      setTimeout(() => {
        this.$emit('hide')
      }, 300)
    },

    onFocus() {
      this.editing = true
      this.focusTimeStamp = Number(new Date())
    },
    handleOnBlur() {
      this.editing = false
      let duration = Number(new Date()) - this.focusTimeStamp
      this.onBlur && this.onBlur(duration)
    },
    onChange() {
      this.$refs.input && this.$refs.input.blur()
    },
    onReturn() {
      this.$refs.input && this.$refs.input.blur()
    },
    onInput() {
      if (this.text.length > this.length) {
        this.text = this.text.slice(0, this.length)
      }
      this.text = this.text.replace(/\D/g, '')
    },
    onCoverClick() {
      this.$refs.input.focus()
    },

    startCountdown() {
      this.countdown = new Countdown(59 * 1000, (isEnd, result) => {
        this.time = result
        if (isEnd) {
          this.cooldown = false
          this.countdown.clear()
        }
      })
    },

    handleConfirmClick() {
      this.$emit('ok', this.text)
    },

    handleSendClick() {
      this.cooldown = true
      this.startCountdown()
      this.$emit('ok')
    }
  }
}
</script>

<style lang="scss" scoped>
.in-otp-dialog {
  &__wrap {
    width: 300px;

    // height: 279px;
    background: #fff;
    border-radius: 14px;
    position: relative;
    box-sizing: border-box;
    padding: 37px 20px 40px;
  }

  &__header {}

  &__tip {
    font-size: 20px;
    font-family: OpenSans-SemiBold, OpenSans;
    color: #333;
    line-height: 20px;
  }

  &__number {
    font-size: 14px;
    font-family: OpenSans-Regular, OpenSans;
    color: #646464;
    line-height: 22px;
    margin-top: 6px;
  }

  &__main {
    position: relative;

    .number-list {
      display: flex;
      margin-top: 15px;
    }

    .number-item {
      flex: 1;
      margin-right: 4px;
      height: 55px;
      background: #fff;
      border-radius: 6px;
      border: 1px solid #ddd;
      font-size: 24px;
      font-family: Helvetica;
      color: #1f1f1f;
      line-height: 55px;
      text-align: center;

      &:last-child {
        margin-right: 0;
      }
    }

    .text-field-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: transparent;
    }

    .text-field {
      position: absolute;
      top: -1000px; //直接设置display:none会无法获取焦点
    }

    .cursor {
      width: 2px;
      height: 80%;
      background-color: #aaa;
      animation: 1s cursorAnimation infinite normal;
      margin-left: 4px;
      margin-top: 10%;
    }
  }

  .otp-info {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;

    &__left {
      display: flex;
      align-items: center;
      font-size: 13px;
      font-family: OpenSans-Regular, OpenSans;
      color: #00903b;
      line-height: 18px;
    }

    &__right {
      .countdown {
        font-size: 13px;
        font-family: Helvetica;
        color: #999;
        line-height: 16px;
      }

      .text-button {
        font-size: 13px;
        font-family: OpenSans-Regular, OpenSans;
        color: #00903b;
        line-height: 18px;
      }
    }
  }

  .submit-button {
    margin: 0 auto;
    margin-top: 20px;
    width: 252px;
  }
}

.icon-close {
  display: block;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 16px;
  right: 16px;
}

.icon-mobile {
  display: block;
  width: 16px;
  height: 16px;
  margin-right: 1px;
}

.row-button {
  display: flex;

  button {
    width: 122px;
  }
}

@keyframes cursorAnimation {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
</style>
