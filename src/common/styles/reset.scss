/* reset style */
article,
aside,
figcaption,
figure,
footer,
header,
main,
nav,
section {
  display: block;
  margin: 0;
  padding: 0;
}

body,
p,
ul,
ol,
li,
dd,
dl,
dt,
h1,
h2,
h3,
h4,
h5,
h6,
form,
input,
textarea {
  margin: 0;
  padding: 0;
}

img {
  border: 0;
}

input,
textarea {
  outline: none;
  appearance: none;
}

body,
button,
input,
select,
textarea,
pre {
  font-family: OpenSans, 'Helvetica Neue', Helvetica, STHeiTi, 'Microsoft YaHei', sans-serif, Arial;
}

html {
  height: 100%;
  text-size-adjust: none;
  -webkit-tap-highlight-color: transparent;
}

body {
  font: 16PX OpenSans, 'Helvetica Neue', Helvetica, STHeiTi, 'Microsoft YaHei', sans-serif, Arial;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  user-select: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}

/* 清除点击虚拟框 */
a,
div,
p,
span,
ul,
li,
i,
img,
input {
  outline: 0;
  text-decoration: none;
  -webkit-tap-highlight-color: rgb(0 0 0 / 0%);
}

a:focus {
  outline: 0;
}

a:link,
a:visited {
  text-decoration: none;
}

a img {
  border: 0 none;
}

a,
img {
  -webkit-touch-callout: none;
}

ul,
ol,
li {
  list-style: none;
}

em,
i {
  font-style: normal;
}

input,
textarea,
select {
  outline: none;
  font-family: 'Helvetica Neue', Helvetica, STHeiTi, 'Microsoft YaHei', sans-serif, Arial;
}

input {
  appearance: none;
}

input[type="submit"] {
  box-sizing: content-box;
}

input[type="text"],
input[type="password"],
input[type="tel"] {
  border: 0 none;
}

textarea {
  overflow-x: hidden;
  overflow-y: auto;
  resize: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

a,
a:hover {
  text-decoration: none;
}

input[type="submit"],
input[type="reset"],
input[type="button"],
button {
  border: 0 none;
  appearance: none;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  appearance: none !important;
  margin: 0;
}

input[type="number"] {
  appearance: textfield;
}

::input-placeholder {
  font-size: 14px;
}

:placeholder {
  font-size: 14px;
}

::placeholder {
  font-size: 14px;
}

:input-placeholder {
  font-size: 14px;
}

.has-header {
  /* prettier-ignore */
  padding-top: 48PX;
}

// #app{
//   padding-top: constant(safe-area-inset-top);
//   padding-top: env(safe-area-inset-top);
// }