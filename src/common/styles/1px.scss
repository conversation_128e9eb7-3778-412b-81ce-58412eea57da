@mixin setLine($c: #c7c7c7) {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  border: 1px solid $c; // ignored
  color: $c;
  height: 200%;
  transform-origin: left top;
  transform: scale3d(0.5, 0.5, 1);
}

@mixin setTopLine($c: #c7c7c7, $t: solid) {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px; // ignored
  border-top: 1px $t $c; // ignored
  color: $c;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

@mixin setBottomLine($c: #c7c7c7, $t: solid) {
  content: ' ';
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px; // ignored
  border-bottom: 1px $t $c; // ignored
  color: $c;
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

@mixin setLeftLine($c: #c7c7c7, $t: solid) {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  width: 1px; // ignored
  bottom: 0;
  border-left: 1px $t $c; // ignored
  color: $c;
  transform-origin: 0 0;
  transform: scaleX(0.5);
}

@mixin setRightLine($c: #c7c7c7, $t: solid) {
  content: ' ';
  position: absolute;
  right: 0;
  top: 0;
  width: 1px; // ignored
  bottom: 0;
  border-right: 1px $t $c; // ignored
  color: $c;
  transform-origin: 100% 0;
  transform: scaleX(0.5);
}

@mixin q-1px($c) {
  position: relative;

  &::before {
    @include setLine($c);
  }
}

@mixin q-1px-t($c, $t: solid) {
  position: relative;

  &::before {
    @include setTopLine($c, $t);
  }
}

@mixin q-1px-b($c, $t: solid) {
  position: relative;

  &::after {
    @include setBottomLine($c, $t);
  }
}

@mixin q-1px-tb($c, $t: solid) {
  position: relative;

  &::before {
    @include setTopLine($c, $t);
  }

  &::after {
    @include setBottomLine($c, $t);
  }
}

@mixin q-1px-l($c, $t: solid) {
  position: relative;

  &::after {
    @include setLeftLine($c, $t);
  }
}

@mixin q-1px-r($c, $t: solid) {
  position: relative;

  &::before {
    @include setRightLine($c, $t);
  }
}
