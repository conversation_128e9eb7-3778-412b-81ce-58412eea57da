@charset "utf-8";

@mixin text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-line($line) {
  overflow: hidden; // 溢出内容隐藏
  text-overflow: ellipsis; // 文本溢出部分用省略号表示
  display: -webkit-box; // 特别显示模式
  -webkit-line-clamp: $line; // 行数
  line-clamp: $line;
  -webkit-box-orient: vertical; // 盒子中内容竖直排列
}

@mixin icon() {
  display: inline-block;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon {
  @include icon;
}

@mixin delete-line($c: #646464) {
  position: relative;

  &::before {
    content: ' ';
    position: absolute;
    top: 51%;
    left: 0;
    width: 100%;
    height: 1px;
    background: $c;
  }
}

// 定制NutUI主题
$indosat-style-color: #00903b;
$primary-color: $indosat-style-color;