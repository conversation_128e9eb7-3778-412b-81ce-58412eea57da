@transition-easing: cubic-bezier(0.215, 0.61, 0.355, 1); // easeOutCubic

.in-transition {
  &--slide-right,
  &--slide-left,
  &--slide-up,
  &--slide-down,
  &--jump-right,
  &--jump-left,
  &--jump-up,
  &--jump-down,
  &--fade,
  &--scale,
  &--rotate,
  &--flip &-leave-active {
    position: absolute;
  }
  &--slide-right,
  &--slide-left,
  &--slide-up,
  &--slide-down &-enter-active,
  &-leave-active {
    transition: transform 0.3s @transition-easing;
  }

  &--slide-up &-enter {
    transform: translate3d(0, 100%, 0);
  }
  &-leave-to {
    transform: translate3d(0, -100%, 0);
  }

  &--slide-down &-enter {
    transform: translate3d(0, -100%, 0);
  }
  &-leave-to {
    transform: translate3d(0, 100%, 0);
  }

  &--fade &-enter-active,
  &-leave-active {
    transition: opacity 0.3s ease-out;
  }
  &-enter,
  &-leave,
  &-leave-to {
    opacity: 0;
  }

  &--jump-right,
  &--jump-left,
  &--jump-up,
  &--jump-down {
    &-enter-active,
    &-leave-active {
      transition: opacity 0.3s, transform 0.3s;
    }

    &-enter,
    &-leave-to {
      opacity: 0;
    }
  }

  &--jump-right {
    &-enter {
      transform: translate3d(-15px, 0, 0);
    }
  }

  &-leave-to {
    transform: translate3d(15px, 0, 0);
  }

  &--jump-left {
    &-enter {
      transform: translate3d(15px, 0, 0);
    }
    &-leave-to {
      transform: translateX(-15px);
    }
  }

  &--jump-up {
    &-enter {
      transform: translate3d(0, 15px, 0);
    }
    &-leave-to {
      transform: translate3d(0, -15px, 0);
    }
  }
  &--jump-down {
    &-enter {
      transform: translate3d(0, -15px, 0);
    }
    &-leave-to {
      transform: translate3d(0, 15px, 0);
    }
  }
}
