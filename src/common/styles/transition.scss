.in-transition--slide-right-leave-active,
.in-transition--slide-left-leave-active,
.in-transition--slide-up-leave-active,
.in-transition--slide-down-leave-active,
.in-transition--jump-right-leave-active,
.in-transition--jump-left-leave-active,
.in-transition--jump-up-leave-active,
.in-transition--jump-down-leave-active,
.in-transition--fade-leave-active,
.in-transition--scale-leave-active,
.in-transition--rotate-leave-active,
.in-transition--flip-leave-active {
  position: absolute;
}

.in-transition--slide-right-enter-active,
.in-transition--slide-right-leave-active,
.in-transition--slide-left-enter-active,
.in-transition--slide-left-leave-active,
.in-transition--slide-up-enter-active,
.in-transition--slide-up-leave-active,
.in-transition--slide-down-enter-active,
.in-transition--slide-down-leave-active {
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.in-transition--slide-right-enter {
  transform: translate3d(-100%, 0, 0);
}

.in-transition--slide-right-leave-to {
  transform: translate3d(100%, 0, 0);
}

.in-transition--slide-left-enter {
  transform: translate3d(100%, 0, 0);
}

.in-transition--slide-left-leave-to {
  transform: translate3d(-100%, 0, 0);
}

.in-transition--slide-up-enter {
  transform: translate3d(0, 100%, 0);
}

.in-transition--slide-up-leave-to {
  transform: translate3d(0, -100%, 0);
}

.in-transition--slide-down-enter {
  transform: translate3d(0, -100%, 0);
}

.in-transition--slide-down-leave-to {
  transform: translate3d(0, 100%, 0);
}

.in-transition--jump-right-enter-active,
.in-transition--jump-right-leave-active,
.in-transition--jump-left-enter-active,
.in-transition--jump-left-leave-active,
.in-transition--jump-up-enter-active,
.in-transition--jump-up-leave-active,
.in-transition--jump-down-enter-active,
.in-transition--jump-down-leave-active,
.in-transition--slide-up-enter-active,
.in-transition--slide-up-leave-active,
.in-transition--slide-down-enter-active,
.in-transition--slide-down-enter-active  {
  transition: opacity 0.3s, transform 0.3s;
}

.in-transition--jump-right-enter,
.in-transition--jump-right-leave-to,
.in-transition--jump-left-enter,
.in-transition--jump-left-leave-to,
.in-transition--jump-up-enter,
.in-transition--jump-up-leave-to,
.in-transition--jump-down-enter,
.in-transition--jump-down-leave-to,
.in-transition--slide-up-enter,
.in-transition--slide-up-leave-to,
.in-transition--slide-down-enter,
.in-transition--slide-down-enter-to {
  opacity: 0;
}

.in-transition--jump-right-enter {
  transform: translate3d(-15px, 0, 0);
}

.in-transition--jump-right-leave-to {
  transform: translate3d(15px, 0, 0);
}

.in-transition--jump-left-enter {
  transform: translate3d(15px, 0, 0);
}

.in-transition--jump-left-leave-to {
  transform: translateX(-15px);
}

.in-transition--jump-up-enter {
  transform: translate3d(0, 15px, 0);
}

.in-transition--jump-up-leave-to {
  transform: translate3d(0, -15px, 0);
}

.in-transition--jump-down-enter {
  transform: translate3d(0, -15px, 0);
}

.in-transition--jump-down-leave-to {
  transform: translate3d(0, 15px, 0);
}

.in-transition--fade-enter-active,
.in-transition--fade-leave-active {
  transition: opacity 0.3s ease-out;
}

.in-transition--fade-enter,
.in-transition--fade-leave,
.in-transition--fade-leave-to {
  opacity: 0;
}

.in-transition--scale-enter-active,
.in-transition--scale-leave-active {
  transition: opacity 0.3s, transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.in-transition--scale-enter,
.in-transition--scale-leave,
.in-transition--scale-leave-to {
  opacity: 0;
  transform: scale3d(0, 0, 1);
}

.in-transition--rotate-enter-active,
.in-transition--rotate-leave-active {
  transition: opacity 0.3s, transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform-style: preserve-3d;
}

.in-transition--rotate-enter,
.in-transition--rotate-leave,
.in-transition--rotate-leave-to {
  opacity: 0;
  transform: scale3d(0, 0, 1) rotate3d(0, 0, 1, 90deg);
}

.in-transition--flip-right-enter-active,
.in-transition--flip-right-leave-active,
.in-transition--flip-left-enter-active,
.in-transition--flip-left-leave-active,
.in-transition--flip-up-enter-active,
.in-transition--flip-up-leave-active,
.in-transition--flip-down-enter-active,
.in-transition--flip-down-leave-active {
  transition: transform 0.3s;
  backface-visibility: hidden;
}

.in-transition--flip-right-enter-to,
.in-transition--flip-right-leave,
.in-transition--flip-left-enter-to,
.in-transition--flip-left-leave,
.in-transition--flip-up-enter-to,
.in-transition--flip-up-leave,
.in-transition--flip-down-enter-to,
.in-transition--flip-down-leave {
  transform: perspective(400px) rotate3d(1, 1, 0, 0deg);
}

.in-transition--flip-right-enter {
  transform: perspective(400px) rotate3d(0, 1, 0, -180deg);
}

.in-transition--flip-right-leave-to {
  transform: perspective(400px) rotate3d(0, 1, 0, 180deg);
}

.in-transition--flip-left-enter {
  transform: perspective(400px) rotate3d(0, 1, 0, 180deg);
}

.in-transition--flip-left-leave-to {
  transform: perspective(400px) rotate3d(0, 1, 0, -180deg);
}

.in-transition--flip-up-enter {
  transform: perspective(400px) rotate3d(1, 0, 0, -180deg);
}

.in-transition--flip-up-leave-to {
  transform: perspective(400px) rotate3d(1, 0, 0, 180deg);
}

.in-transition--flip-down-enter {
  transform: perspective(400px) rotate3d(1, 0, 0, 180deg);
}

.in-transition--flip-down-leave-to {
  transform: perspective(400px) rotate3d(1, 0, 0, -180deg);
}
