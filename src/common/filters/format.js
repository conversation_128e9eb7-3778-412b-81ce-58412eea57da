import dayjs from 'dayjs'
import { joinSymbol } from '../utils'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
import timezone from 'dayjs/plugin/timezone'
dayjs.extend(timezone)

export function installFormatTime(Vue) {
  Vue.filter('formatTime', (val, format) => {
    if (!val) return ''
    let v = val
    if (typeof val === 'string' || typeof val === 'number') {
      v = new Date(val)
    }
    return dayjs(v)
      .tz('Asia/Jakarta')
      .format(format || 'DD/MM/YYYY HH:mm:ss')
  })
}

export function installFormatDate(Vue) {
  Vue.filter('formatDate', (val, format) => {
    if (!val) return ''
    let v = val
    if (typeof val === 'string' || typeof val === 'number') {
      v = new Date(val)
    }
    return dayjs(v)
      .tz('Asia/Jakarta')
      .format(format || 'DD/MM/YYYY')
  })
}

export function installFormatCurrency(Vue) {
  Vue.filter('formatCurrency', (val, decimals) => {
    val = (val + '').replace(/[^0-9+-Ee.]/g, '')
    let n = !isFinite(+val) ? 0 : +val,
      prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
      sep = '.',
      dec = ',',
      s = '',
      toFixedFix = function (n, prec) {
        let k = Math.pow(10, prec)
        return '' + Math.ceil(n * k) / k
      }

    //获取当前地区的千分位和小数点
    // let sepAndDes = Number(123456 / 100)
    //   .toLocaleString()
    //   .replace(/\d/g, '');
    // if (sepAndDes.length == 2) {
    //   sep = sepAndDes.substring(0, 1);
    //   dec = sepAndDes.substring(1, 2);
    // }

    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.')
    let re = /(-?\d+)(\d{3})/
    while (re.test(s[0])) {
      s[0] = s[0].replace(re, '$1' + sep + '$2')
    }
    if ((s[1] || '').length < prec) {
      s[1] = s[1] || ''
      s[1] += new Array(prec - s[1].length + 1).join('0')
    }
    return s.join(dec)
  })
}

export function installMaskNumber(Vue) {
  Vue.filter('maskNumber', (val, headSize = 3, tailSize = 4) => {
    if (val.length < headSize + tailSize) {
      return val
    } else {
      let maskLength = val.length - headSize - tailSize
      let maskStr = ''
      while (maskLength-- > 0) maskStr += '*'

      return val.substr(0, headSize) + maskStr + val.substr(val.length - tailSize, val.length)
    }
  })
}

export function installJoinSymbol(Vue) {
  Vue.filter('joinSymbol', (val, num = 4, symbol = ' ') => {
    return joinSymbol(val, num, symbol)
  })
}
