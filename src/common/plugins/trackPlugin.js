import { TrackEvent } from '@/common/track'

export const TrackPlugin = {
  install(Vue, options = {}, router) {
    const trackEvent = new TrackEvent(options)
    if (router) {
      router.afterEach((to, from) => {
        const screenName = to.meta?.screenName
        if (screenName) {
          trackEvent.trackScreen(screenName)
        }
      })
    }

    Vue.prototype.$track = trackEvent
  }
}
