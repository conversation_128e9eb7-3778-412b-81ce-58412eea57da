import {
  installFormatCurrency,
  installFormatDate,
  installFormatTime,
  installMaskNumber,
  installJoinSymbol
} from '../filters'
import { installBridge } from '@/common/bridge'

export const InCommonPlugin = {
  install(Vue, options = {}) {
    // 注册filter
    installFormatCurrency(Vue)
    installFormatDate(Vue)
    installFormatTime(Vue)
    installMaskNumber(Vue)
    installJoinSymbol(Vue)

    installXMP()

    // 注册directives

    // 原型挂载
    installBridge(Vue, {
      router: options.router,
      deeplinkUrl: options.deeplinkUrl
    })
  }
}

function installXMP() {
  // 获取ua中的statusBarVW
  try {
    var statusBarHeight = navigator.userAgent.match(/statusBarHeight:.*?[;|)]/g)[0]
    statusBarHeight = statusBarHeight.substring(16, statusBarHeight.length - 1)
    var width = navigator.userAgent.match(/width:.*?[;|)]/g)[0]
    width = width.substring(6, width.length - 1)
    var vw = (parseFloat(statusBarHeight) / parseFloat(width)) * 100
    document.documentElement.style.setProperty('--statusBarHeight', vw + 'vw')
    window.xmpStatusBarHeight = vw + 'vw'
  } catch (e) {
    document.documentElement.style.setProperty('--statusBarHeight', '36px')
    window.xmpStatusBarHeight = '36px'
  }
}
