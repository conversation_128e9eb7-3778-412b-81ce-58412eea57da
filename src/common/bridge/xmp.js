/* eslint-disable @typescript-eslint/no-empty-function */
// 工具方法：判断是否是XMP环境
function isXMPIOS() {
  return window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.xmp
}
function isXMP() {
  return window.xmp || isXMPIOS()
}

// 通道所需要的内存存储
window.callbackCache = {}
window.listenerCache = {}

//原生 调用 js
window.onNativeCall = function (argStr) {
  const arg = JSON.parse(argStr)
  if (arg.callbackId) {
    if (window.callbackCache[arg.callbackId]) {
      window.callbackCache[arg.callbackId](arg)
    }
  } else {
    if (window.listenerCache[arg.method]) {
      return window.listenerCache[arg.method](arg)
    }
  }
}

//js 监听 原生
window.xmpOn = function (method, listener) {
  if (listener) {
    window.listenerCache[method] = function (arg) {
      return listener(arg.data)
    }
  }
}

//js 调用 原生
window.xmpCall = function (method, data, callback) {
  let callbackId = Math.random().toString()
  if (window.koosdk) {
    // 兼容KOOSDK 表示KOOU Android PPDWebUI的版本，callbackId只支持int
    const min = 0
    const max = 2000000000
    callbackId = Math.floor(Math.random() * (max - min + 1)) + min
  }
  let arg = {
    method: method,
    data: data,
    callbackId: callbackId
  }
  if (window.xmp) {
    window.xmp.exec(JSON.stringify(arg))
  } else if (window.webkit && window.webkit.messageHandlers.xmp) {
    window.webkit.messageHandlers.xmp.postMessage(JSON.stringify(arg))
  } else if (window.koosdk) {
    // 兼容KOOSDK
    window.koosdk.exec(JSON.stringify(arg))
  } else if (window.webkit && window.webkit.messageHandlers.koosdk) {
    // 兼容KOOSDK
    window.webkit.messageHandlers.koosdk.postMessage(JSON.stringify(arg))
  } else {
    return
  }
  if (callback) {
    window.callbackCache[callbackId] = function (arg) {
      window.callbackCache[callbackId] = undefined
      callback(arg.data)
    }
  }
}

function stringify(message) {
  try {
    let seen = []
    let log = JSON.stringify(message, function (key, val) {
      if (val !== null && typeof val === 'object') {
        if (seen.indexOf(val) >= 0) {
          return
        }
        seen.push(val)
      }
      return val
    })
    return log
  } catch (error) {
    return error.toString()
  }
}

//iOS 支持console.log打印到控制台，（Android系统本身是可以输出H5的console.log）
if (isXMPIOS) {
  let trueConsoleLog = console.log
  console.log = function (...args) {
    if (args) {
      for (let i = 0; i < args.length; i++) {
        window.xmpCall(
          'log',
          {
            msg: stringify(args[i])
          },
          null
        )
      }
      trueConsoleLog(...args)
    }
  }
}

function handleXMPCacheMode(targetUrl) {
  //如果target url上配置了xmpCacheMode则直接使用，否则使用当前url的xmpCacheMode
  if (targetUrl.indexOf('xmpCacheMode=') === -1 && window.location.href.indexOf('xmpCacheMode=') !== -1) {
    let url = new URL(targetUrl)
    if (window.location.href.indexOf('xmpCacheMode=noCache') !== -1) {
      url.searchParams.append('xmpCacheMode', 'noCache')
    } else if (window.location.href.indexOf('xmpCacheMode=forceCache') !== -1) {
      url.searchParams.append('xmpCacheMode', 'forceCache')
    } else if (window.location.href.indexOf('xmpCacheMode=offlinePackage') !== -1) {
      url.searchParams.append('xmpCacheMode', 'offlinePackage')
    } else if (window.location.href.indexOf('xmpCacheMode=forceCacheOnlyAndroid') !== -1) {
      url.searchParams.append('xmpCacheMode', 'forceCacheOnlyAndroid')
    } else if (window.location.href.indexOf('xmpCacheMode=browser') !== -1) {
      url.searchParams.append('xmpCacheMode', 'browser')
    }
    return url.toString()
  }

  if (targetUrl.indexOf('cacheMode=') === -1 && window.location.href.indexOf('cacheMode=') !== -1) {
    let url = new URL(targetUrl)
    if (window.location.href.indexOf('cacheMode=noCache') !== -1) {
      url.searchParams.append('cacheMode', 'noCache')
    } else if (window.location.href.indexOf('cacheMode=forceCache') !== -1) {
      url.searchParams.append('cacheMode', 'forceCache')
    } else if (window.location.href.indexOf('cacheMode=offlinePackage') !== -1) {
      url.searchParams.append('cacheMode', 'offlinePackage')
    } else if (window.location.href.indexOf('cacheMode=forceCacheOnlyAndroid') !== -1) {
      url.searchParams.append('cacheMode', 'forceCacheOnlyAndroid')
    } else if (window.location.href.indexOf('cacheMode=browser') !== -1) {
      url.searchParams.append('cacheMode', 'browser')
    }
    return url.toString()
  }
  return targetUrl
}

const onShowListenerList = []

export const xmp = {
  openContainer: function (url, replace = false) {
    url = handleXMPCacheMode(url)
    window.xmpCall(
      'openContainer',
      {
        replace: replace,
        url: url
      },
      function (data) {}
    )
  },
  closeContainer: function (data) {
    window.xmpCall('closeContainer', data ? data : {}, function (data) {})
  },
  goBackToContainer: function (patternOrNum, data) {
    window.xmpCall(
      'goBackToContainer',
      {
        data: data || {},
        patternOrNum
      },
      function (data) {}
    )
  },
  notifyH5: function (event, data) {
    window.xmpCall(
      'notifyH5',
      {
        data: data || {},
        event
      },
      function (data) {}
    )
  },
  dismissBackButton: function () {
    window.xmpCall('dismissBackButton', {}, function (data) {})
  },
  onShow: function (listener) {
    onShowListenerList.push(listener)
    if (isXMP()) {
      window.xmpOn('onShow', function (data) {
        for (let index = 0; index < onShowListenerList.length; index++) {
          const func = onShowListenerList[index]
          func(data)
        }
      })
    } else {
      window.xmpOn('activedPage', function (data) {
        for (let index = 0; index < onShowListenerList.length; index++) {
          const func = onShowListenerList[index]
          func(data)
        }
      }) // 兼容KOOSDK
    }
  },
  onHide: function (listener) {
    if (isXMP()) {
      window.xmpOn('onHide', listener)
    } else {
      window.xmpOn('leavePage', listener) // 兼容KOOSDK
    }
  },
  onBackPress: function (listener) {
    if (isXMP()) {
      window.xmpOn('onBackPress', listener)
    } else {
      window.xmpOn('interceptBackPress', listener) // 兼容KOOSDK
    }
  },
  onEvent: function (event, listener) {
    window.xmpOn('onEvent' + event, listener)
  },
  changeTab(index) {
    window.xmpCall('check_tab', { index }, (data) => {})
  },

  enableTabs(enable) {
    window.xmpCall('enable_tabs', { enable }, (data) => {})
  },

  // type: location/contact/camera/notification
  requestPermission(type) {
    return new Promise((r) => {
      window.xmpCall('request_permission', { type }, (data) => {
        r(data)
      })
    })
  },

  //  type: location/appList/contact
  collectPrivacyData(type) {
    return new Promise((r) => {
      window.xmpCall('collect_privacy_data', { type }, (data) => {
        r(data)
      })
    })
  },

  getGPS() {
    return new Promise((r, s) => {
      window.xmpCall('get_gps', {}, (data) => {
        r(data)
      })
    })
  },

  scan() {
    return new Promise((r, s) => {
      window.xmpCall('scan', {}, (data) => {
        r(data)
      })
    })
  },

  getZolozMetaInfo() {
    return new Promise((r, s) => {
      window.xmpCall('zoloz_get_meta', {}, (data) => {
        r(data)
      })
    })
  },

  zolozLiveFace(data) {
    return new Promise((r, s) => {
      window.xmpCall('zoloz_start_detect', { rsa: data.rsa, cfg: data.cfg }, (data) => {
        r(data)
      })
    })
  },

  collectTongdunData(data) {
    window.xmpCall('collect_tongdun', { ...data, bizType: data.bizType }, (data) => {})
  },

  getAdaDeviceRiskInfo(data) {
    return new Promise((r, s) => {
      window.xmpCall('get_ada_device_risk', {}, (data) => {
        r(data)
      })
    })
  },

  getAppInfo(data) {
    return new Promise((r, s) => {
      window.xmpCall('get_app_info', {}, (data) => {
        r(data)
      })
    })
  },

  // interval 单位秒
  runGPS(data) {
    window.xmpCall('run_gps', { ...data, interval: data.interval }, (data) => {})
  },

  getDeviceInfo(data) {
    return new Promise((r, s) => {
      window.xmpCall('get_device_info', {}, (data) => {
        r(data)
      })
    })
  },

  openHomePage(data) {
    return window.xmpCall('open_home_page', { ...data })
  },
  setSwipeBack(data) {
    window.xmpCall('set_swipe_back', { ...data }, () => {})
  },
  saveLenderUserToken(data) {
    return new Promise((r) => {
      window.xmpCall('save_lender_user_token', { ...data }, (data) => {
        r(data)
      })
    })
  },
  clearLenderUserToken(data) {
    return new Promise((r) => {
      window.xmpCall('clear_lender_user_token', { ...data }, (data) => {
        r(data)
      })
    })
  },
  openNativeLoginPage(data) {
    return new Promise((r) => {
      window.xmpCall('open_native_login_page', { ...data }, (data) => {
        r(data)
      })
    })
  },
  preferBorrower(data) {
    return new Promise((r) => {
      window.xmpCall('prefer_borrower', { ...data }, (data) => {
        r(data)
      })
    })
  },
  openWebView(data) {
    return new Promise((r) => {
      window.xmpCall('open_webview', { ...data }, (data) => {
        r(data)
      })
    })
  },

  openDeeplink(data) {
    return new Promise((r) => {
      window.xmpCall('open_deeplink', { ...data }, (data) => {
        r(data)
      })
    })
  },

  callNetworkRequest(data) {
    return new Promise((r) => {
      window.xmpCall('call_network_request', { ...data }, (data) => {
        r(data)
      })
    })
  },
  notifyNative(event, data) {
    window.xmpCall('notifyNative', {
      data: data || {},
      event
    })
  },
  closeAll() {
    window.xmpCall('closeAll')
  },
  loginByAdakami() {
    return new Promise((r, s) => {
      window.xmpCall('login_by_adakami', {}, (data) => {
        r(data)
      })
    })
  },
  getFirebasePushToken() {
    return new Promise((r, s) => {
      window.xmpCall('get_push_token', {}, (data) => {
        r(data)
      })
    })
  },
  getAppsflyerInfo() {
    return new Promise((r, s) => {
      window.xmpCall('get_appsflyer_data', {}, (data) => {
        r(data)
      })
    })
  },

  getPushLog() {
    return new Promise((r, s) => {
      window.xmpCall('get_push_log', {}, (data) => {
        r(data.result)
      })
    })
  },

  call: window.xmpCall,
  isXMP
}
