import { isString, appendUrlParam, browser } from '@/common/utils'
import { xmp } from './xmp'
import Vue from 'vue'
import Router, { RawLocation } from 'vue-router'
import { InLoading } from '../components/InLoading'

export type BridgeOption = {
  router: Router
  deeplinkUrl: string
}

export type TGPSInfo = {
  latitude: number
  longitude: number
  province: string
  city: string
  area: string
  detail: string
}

export class Bridge {
  /**
   * @description: vue-router 实例
   */
  #router

  #deeplinkUrl

  /**
   * @constructor
   */
  constructor(options: BridgeOption) {
    const { router, deeplinkUrl } = options
    this.#router = router
    this.#deeplinkUrl = deeplinkUrl
  }

  /**
   * @description: 页面跳转， 建议使用router name跳转
   */
  link(target: string | RawLocation, config: Record<string, string> | { replace: boolean } = {}) {
    const targetIsString = isString(target)
    if (!targetIsString && !this.#router) {
      return Promise.reject('没有router实例时，link target只能为字符串')
    }

    const path = targetIsString ? <string>target : this.#router.resolve(target)?.href
    const { replace, ...otherConfig } = config
    if (browser.app) {
      const url = targetIsString ? path : `${location.protocol}//${location.host}${location.pathname}${path}`

      const resultUrl = appendUrlParam(url, otherConfig)

      if (!replace) {
        xmp.openContainer(resultUrl, <boolean>replace ?? false)
        return
      } else if (!targetIsString) {
        this.#router.replace(target)
      } else {
        location.replace(path)
      }
    } else {
      if (this.#router && !targetIsString) {
        if (replace) {
          this.#router.replace(target)
        } else {
          this.#router.push(target)
        }
      } else {
        if (replace) {
          location.href = path
        } else {
          location.replace(path)
        }
      }
    }
  }

  /**
   * bridge.back will preserve the root xmp page
   * bridge.back(-1)
   * @description: 返回
   */
  back(number: number, data?: unknown) {
    if (browser.app) {
      xmp.goBackToContainer(number, data)
    } else {
      history.go(number)
    }
  }

  /**
   * @description: 关闭webview
   */
  close(data?: unknown) {
    if (browser.app) {
      xmp.closeContainer(data)
    } else {
      history.back()
    }
  }

  /**
   * @description: webview再次显示时调用此方法
   * 页面创建第一次展示时不会调用该方法，data是其他页面关闭时的传参
   */
  onShow<T>(callback: (data: T) => void) {
    if (browser.app) {
      xmp.onShow(callback)
    } else {
      console.warn('浏览器环境暂不支持<onShow>方法')
    }
  }

  /**
   * @description: webview隐藏时调用此方法
   */
  onHide<T>(callback: (data: T) => void) {
    if (browser.app) {
      xmp.onHide(callback)
    } else {
      console.warn('浏览器环境暂不支持<onHide>方法')
    }
  }

  /**
   * @description: h5给通知客户端发送通知
   * @param {*} callback
   * @return {*}
   */
  notify(eventName: string, data: unknown) {
    if (browser.app) {
      xmp.notifyH5(eventName, data)
    } else {
      console.warn('浏览器环境不支持<notify>方法')
    }
  }

  /**
   * @description: 监听事件
   */
  onEvent<T>(eventName: string, callback: (data: T) => void) {
    if (browser.app) {
      xmp.onEvent(eventName, callback)
    } else {
      console.warn('浏览器环境不支持<onEvent>方法')
    }
  }

  /**
   * @description: 拦截返回键
   * 包括Android物理返回键，返回字符串例如："true"，表示拦截本次点击，否则不拦截
   */
  onBackPress(callback: () => string) {
    if (browser.app) {
      xmp.onBackPress(callback)
    } else {
      console.warn('浏览器环境不支持<onBackPress>方法')
    }
  }

  /**
   * @description: 隐藏返回按钮
   */
  dismissBackButton() {
    if (browser.app) {
      xmp.dismissBackButton()
    } else {
      console.warn('浏览器环境不支持<dismissBackButton>方法')
    }
  }

  /**
   * @description: 选中tab
   */
  changeTab(index: number) {
    if (browser.app) {
      xmp.changeTab(index)
    } else {
      console.warn('浏览器环境不支持<changeTab>方法')
    }
  }

  /**
   * @description: 设置tab是否可点击切换
   */
  enableTabs(enable: boolean) {
    if (browser.app) {
      xmp.enableTabs(enable)
    } else {
      console.warn('浏览器环境不支持<enableTabs>方法')
    }
  }

  /**
   * @description: 申请权限
   * 拒绝时会引导到设置页开启
   */
  requestPermission(type: 'location' | 'contact' | 'camera' | 'notification'): Promise<{ granted: boolean }> {
    if (browser.app) {
      return xmp.requestPermission(type)
    } else {
      console.warn('浏览器环境不支持<requestPermission>方法')
      return Promise.resolve({ granted: true })
    }
  }

  /**
   * @description: 采集各类隐私数据，并自动上传
   */
  collectPrivacyData(type: 'location' | 'contact' | 'appList') {
    if (browser.app) {
      return xmp.collectPrivacyData(type)
    } else {
      console.warn('浏览器环境不支持<collectPrivacyData>方法')
    }
  }

  /**
   * @description: 单次采集gps数据
   * {latitude: 10.11, longitude: 20.11, province: '', city: '', district: '', detail: ''}
   */
  getGPS() {
    if (browser.app) {
      return xmp.getGPS()
    } else {
      console.warn('浏览器环境不支持<getGPS>方法')
      return Promise.resolve({})
    }
  }

  /**
   * @description: 扫码
   */
  scan() {
    if (browser.app) {
      return xmp.scan()
    } else {
      console.warn('浏览器环境不支持<scan>方法')
    }
  }

  /**
   * @description: zoloz活体获取meta
   */
  getZolozMetaInfo() {
    if (browser.app) {
      return xmp.getZolozMetaInfo()
    } else {
      console.warn('浏览器环境不支持<getZolozMetaInfo>方法')
    }
  }

  /**
   * @description: zoloz活体
   */
  zolozLiveFace(data: { rsa: string; cfg: string }) {
    if (browser.app) {
      return xmp.zolozLiveFace(data)
    } else {
      console.warn('浏览器环境不支持<zolozLiveFace>方法')
    }
  }

  /**
   * @description: 采集同盾数据，并自动上传
   */
  collectTongdunData(data: { url: string; headers: Record<string, string>; bizType: string }) {
    if (browser.app) {
      xmp.collectTongdunData(data)
    } else {
      console.warn('浏览器环境不支持<collectTongdunData>方法')
    }
  }

  /**
   * @description: 获取设备风险信息（仅自研方案）
   */
  getAdaDeviceRiskInfo() {
    if (browser.app) {
      return xmp.getAdaDeviceRiskInfo()
    } else {
      return Promise.resolve({})
    }
  }

  /**
   * @description: 获取原生app信息
   */
  getAppInfo() {
    if (browser.app) {
      return xmp.getAppInfo()
    } else {
      return Promise.resolve({})
    }
  }

  /**
   * @description: 触发持续性的gps采集，并自动上传
   * interval 单位秒
   */
  runGPS(data: { url: string; headers: Record<string, string>; interval: number }) {
    if (browser.app) {
      xmp.runGPS(data)
    } else {
      console.warn('浏览器环境不支持<runGPS>方法')
    }
  }

  /**
   * @description: 读取设备信息
   */
  getDeviceInfo(): Promise<{
    gaid: string
    aid: string
    idfv: string
    idfa: string
    osVersion: string
    model: string
  }> {
    if (browser.app) {
      return xmp.getDeviceInfo()
    } else {
      return Promise.resolve({
        gaid: 'web not support',
        aid: 'web not support',
        idfv: 'web not support',
        idfa: 'web not support',
        osVersion: 'web not support',
        model: 'web not support'
      })
    }
  }

  /**
   * @description: 1成功、0失败、2有权限但未获取到
   * @return {*}
   */
  async getGPSWithPermission(hideLoading = false): Promise<{ status: 0 | 1 | 2; data: TGPSInfo | null }> {
    if (browser.app) {
      const { granted } = await this.requestPermission('location')
      console.log(granted)
      if (granted) {
        if (!hideLoading) {
          InLoading.show()
        }
        const gpsInfo = await this.getGPS()
        if (!hideLoading) {
          InLoading.close()
        }
        return {
          status: gpsInfo.latitude && gpsInfo.longitude ? 1 : 2,
          data: gpsInfo
        }
      } else {
        return {
          status: 0,
          data: null
        }
      }
    } else {
      return Promise.resolve({
        status: 1,
        data: {
          latitude: 0,
          longitude: 0,
          province: '',
          city: '',
          area: '',
          detail: ''
        }
      })
    }
  }

  /**
   * @description: 1成功、0失败
   * @return {*}
   */
  async scanWithPermission(): Promise<{ status: 0 | 1; data: string | null }> {
    if (browser.app) {
      const { granted } = await this.requestPermission('camera')
      if (granted) {
        const result = await this.scan()
        return {
          status: 1,
          data: result?.result
        }
      } else {
        return {
          status: 0,
          data: null
        }
      }
    } else {
      console.warn('浏览器环境不支持<scanWithPermission>方法')
      return {
        status: 0,
        data: null
      }
    }
  }

  async openHomePage() {
    if (browser.app) {
      const base = location.origin
      xmp.openHomePage({
        urls: [
          `${base}/lender/src/module/lender/index.html#/home`,
          `${base}/lender/src/module/lender/index.html#/asset`,
          `${base}/lender/src/module/lender/index.html#/account-center`
          // 'http://************:5173/src/module/lender/#/home',
          // 'http://************:5173/src/module/lender/#/asset',
          // 'http://************:5173/src/module/lender/#/account-center'
          // 'http://***************:5173/src/module/lender/#/home',
          // 'http://***************:5173/src/module/lender/#/asset',
          // 'http://***************:5173/src/module/lender/#/account-center'
        ]
      })
    } else {
      console.warn('浏览器环境不支持<openHomePage>方法')
    }
  }

  async setSwipeBack(enable = true) {
    if (browser.app) {
      xmp.setSwipeBack({
        enable
      })
    } else {
      console.warn('浏览器环境不支持<setSwipeBack>方法')
    }
  }

  async saveLenderUserToken(token: string) {
    if (browser.app) {
      xmp.saveLenderUserToken({
        token
      })
    } else {
      console.warn('浏览器环境不支持<saveLenderUserToken>方法')
    }
  }

  async clearLenderUserToken() {
    if (browser.app) {
      xmp.clearLenderUserToken()
    } else {
      console.warn('浏览器环境不支持<clearLenderUserToken>方法')
    }
  }

  async openNativeLoginPage() {
    if (browser.app) {
      xmp.openNativeLoginPage()
    } else {
      console.warn('浏览器环境不支持<openNativeLoginPage>方法')
    }
  }

  async preferBorrower(mobile: string) {
    if (browser.app) {
      xmp.preferBorrower({ mobile })
    } else {
      console.warn('浏览器环境不支持<clickBorrower>方法')
    }
  }

  async openWebView(url: string) {
    if (browser.app) {
      xmp.openWebView({ url })
    } else {
      console.warn('浏览器环境不支持<openWebView>方法')
    }
  }

  async openDeeplink(deepLink: string) {
    if (browser.app) {
      xmp.openDeeplink({ deepLink })
    } else {
      console.warn('浏览器环境不支持<openDeeplink>方法')
    }
  }
  async notifyNative(event: string, data: unknown) {
    if (browser.app) {
      xmp.notifyNative(event, data)
    } else {
      console.warn('浏览器环境不支持<openNativeLoginPage>方法')
    }
  }
  async callNetworkRequest(data: { api: string; params: any; hideLoading?: boolean; hideToast?: boolean }) {
    if (browser.app) {
      return xmp.callNetworkRequest(data)
    } else {
      console.warn('浏览器环境不支持<callNetworkRequest>方法')
    }
  }
  async closeAll() {
    if (browser.app) {
      xmp.closeAll()
    } else {
      console.warn('浏览器环境不支持<closeAll>方法')
    }
  }

  async loginByAdakami() {
    if (browser.app) {
      return await xmp.loginByAdakami()
    } else {
      console.warn('浏览器环境不支持<loginByAdakami>方法')
    }
  }

  async getFirebasePushToken(): Promise<{ token?: string; notificationOn: boolean } | undefined> {
    if (browser.app) {
      return xmp.getFirebasePushToken()
    } else {
      console.warn('浏览器环境不支持<getFirebasePushToken>方法')
    }
  }

  async getAppsflyerInfo() {
    if (browser.app) {
      return xmp.getAppsflyerInfo()
    } else {
      console.warn('浏览器环境不支持<getAppsflyerInfo>方法')
    }
  }

  async getPushLog(): Promise<Array<{ flowNo: string; event: string }> | undefined> {
    if (browser.app) {
      return xmp.getPushLog()
    } else {
      console.warn('浏览器环境不支持<getAppsflyerInfo>方法')
    }
  }
}

export function installBridge(Vue: any, options: BridgeOption) {
  const { router, deeplinkUrl } = options
  const bridge = new Bridge({
    router,
    deeplinkUrl
  })
  Vue.prototype.$bridge = bridge
}

export const useBridge = () => {
  return Vue.prototype.$bridge as Bridge
}
