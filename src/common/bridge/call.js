import { browser } from '@/common/utils';

let idCounter = 0;

/**
 * @description: 调用原生方法
 * @param {*} api
 * @param {*} params
 * @param {*} needCallback
 * @return {*}
 */
export function callNative(api, params, needCallback = false) {
  return new Promise(function (resolve, reject) {
    try {
      // 创建全局回调函数
      if (needCallback) {
        const callback = `cb_${idCounter++}`;
        window[callback] = (data) => {
          !params.cb && resolve(data);
          params.cb && params.cb(data);
          setTimeout(() => delete window[callback], 0);
        };
        params.callback = callback;
      }

      // 调用原生方法
      if (browser.android) {
        // Android
        if (window.JSHandle) {
          if (Object.keys(params).length === 0) {
            //避免空对象stringify后以{}为参数，安卓客户端无法识别
            window.JSHandle[api]();
          } else {
            window.JSHandle[api](JSON.stringify(params));
          }
        }
      } else if (browser.iOS) {
        // iOS
        if (window.webkit) window.webkit.messageHandlers[api].postMessage(params);
      }
      if (!needCallback) {
        resolve();
      }
    } catch (err) {
      reject(err);
    }
  });
}
