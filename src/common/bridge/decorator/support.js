import { versionAvailable } from '@/common/utils';
import { NotSupportStatus } from '../data';
/**
 * @description: 判断当前app版本是否支持该方法
 * @param {*} version
 * @return {*}
 */
export function Support(version) {
  return function (target, funcName, descriptor) {
    const memberFunc = descriptor.value.bind(target);

    if (typeof memberFunc !== 'function') {
      throw new SyntaxError('Only functions can be use `support` decorator.');
    }

    return {
      ...descriptor,
      async value(...args) {
        try {
          if (versionAvailable(version)) {
            return memberFunc(...args);
          } else {
            return Promise.reject(NotSupportStatus);
          }
        } catch (e) {
          return Promise.reject(e);
        }
      }
    };
  };
}
