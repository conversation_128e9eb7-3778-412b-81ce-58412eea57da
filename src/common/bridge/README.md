# App Bridge
> （使用demo见demo目录）
## Webview
### 1. useragent: 

## Bridge 方法

### 1. openNewWeb

- 描述： 打开新的 webview
- 参数:
  | 字段名称 | 字段说明 | 类型 | 默认值 | 备注 |
  | ------------ | ----------------------------------------------- | ------- | ------ | ---- |
  | nativeHeader | 是否使用原生 header | Boolean | true | - |
  | title | header 标题（只有 nativeHeader 为 true 时有效） | string | - | - |
  | url | h5 链接 | string | - | - |
