import { RouteConfig } from 'vue-router'
import { ProductLine } from '../constant/routerConstant'

export const generateScreenName = (screenName: string) => {
  return `${ProductLine}_${screenName}`
}

const routes: RouteConfig[] = [
  {
    name: 'Entry',
    path: '/',
    component: () => import('../views/Entry'),
    meta: {
      title: '',
      screenName: generateScreenName('入口页'),
      ignoreLogin: true
    }
  },
  {
    name: 'Login',
    path: '/login',
    component: () => import('../views/Login'),
    meta: {
      title: '',
      screenName: generateScreenName('手机号输入页'),
      ignoreLogin: true
    }
  },
  {
    name: 'Main',
    path: '/main',
    component: () => import('../views/Main'),
    redirect: '/main/home',
    children: [
      {
        name: 'Home',
        path: 'home',
        component: () => import('../views/Home'),
        meta: {
          title: '',
          screenName: generateScreenName('首页'),
          ignoreLogin: true
        }
      },
      {
        name: 'Me',
        path: 'me',
        component: () => import('../views/Me'),
        meta: {
          title: '',
          screenName: generateScreenName('个人中心')
        }
      }
    ]
  },
  {
    name: 'ApplyOTP',
    path: '/apply-otp',
    component: () => import('../views/ApplyOTP'),
    meta: {
      title: '',
      screenName: generateScreenName('验证码输入页'),
      hideHeader: true,
      ignoreLogin: true
    }
  },

  {
    name: 'ContractList',
    path: '/contract-list',
    component: () => import('../views/ContractList'),
    meta: {
      title: '',
      screenName: generateScreenName('协议列表')
    }
  },
  {
    name: 'Settings',
    path: '/settings',
    component: () => import('../views/Settings'),
    meta: {
      title: '',
      screenName: generateScreenName('Settings')
    }
  },
  {
    name: 'CancelAccountNotice',
    path: '/cancel-account-notice',
    component: () => import('../views/CancelAccountNotice'),
    meta: {
      title: '',
      screenName: generateScreenName('注销账户须知')
    }
  },
  {
    name: 'CancelAccountOtp',
    path: '/cancel-account-otp',
    component: () => import('../views/CancelAccountOtp'),
    meta: {
      title: '',
      screenName: generateScreenName('注销账户验证')
    }
  },
  {
    name: 'CancelAccountResult',
    path: '/cancel-account-result',
    component: () => import('../views/CancelAccountResult'),
    meta: {
      title: '',
      screenName: generateScreenName('注销账户结果'),
      ignoreLogin: true
    }
  },
  {
    name: 'ChannelDetail',
    path: '/channel-detail',
    component: () => import('../views/ChannelDetail'),
    meta: {
      title: '',
      screenName: generateScreenName('渠道详情'),
      ignoreLogin: true
    }
  },
  {
    name: 'ContractDetail',
    path: '/contract-detail',
    component: () => import('../views/ContractDetail'),
    meta: {
      title: '',
      screenName: generateScreenName('协议详情'),
      ignoreLogin: true
    }
  }
]

export default routes
