import Vue from 'vue'
import Router from 'vue-router'
import routes from './routes'
import useUserInfo from '@/module/default/store/modules/userInfo'
import { useBridge } from '@/common/bridge'

const store = () => {
  return useUserInfo()
}
Vue.use(Router)

const router = new Router({
  mode: 'hash',
  routes: routes
})

// 路由守卫的next()不会尊重replace和push的差异，统一变成'push'效果
// 所以我们改写下replace函数，手动加一个param: __replace=true，用于区分replace和push
const origin = Router.prototype.replace
Router.prototype.replace = function (location) {
  // 跳转需要用name ， 使用path会导致__replace参数丢失
  origin.call(this, {
    ...location,
    params: {
      ...(location.params || {}),
      __replace: true
    }
  })
}

// router.afterEach(async (to, from, next) => {
//   // 统一处理 title
//   document.title = to.meta?.title || 'AdaKami'
// })

router.beforeEach(async (to, from, next) => {
  //判断是否需要登录
  if (!to.meta.ignoreLogin && !store().isLogin) {
    const bridge = useBridge()
    bridge.link({ name: 'Login' })
    return
  } else {
    next()
    return
  }
})

router.beforeEach((to, from, next) => {
  // 统一处理 title
  document.title = to.meta?.title || 'Cashcerdas'
  next()
})

export default router
