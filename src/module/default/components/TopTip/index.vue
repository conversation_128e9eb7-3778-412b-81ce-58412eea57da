<template>
  <div class="top-tip">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'TopTip',

  components: {},

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.top-tip {
  // position: absolute;
  // top: 0;
  // left: 0;
  width: 100%;
  background: #ecfbe7;
  font-size: 12px;
  font-family: OpenSans-Regular, OpenSans;
  font-weight: 400;
  color: #0eb966;
  line-height: 16px;
  padding: 10px 16px;
  box-sizing: border-box;
  text-align: left;
  // height: 52px;
}
</style>
