<template>
  <div class="live-tutorial">
    <img class="img-live-view" :src="require('./assets/head-view.png')" />
    <p class="live-tutorial__tip">
      Pastikan posisi wajah Anda sesuai dengan arahan dan pastikan pencerahan sekitar cukup
    </p>
    <div class="img-example">
      <div class="img-example__item correct">
        <img class="img-example__img" :src="require('./assets/head01.png')" />
        <p class="img-example__desc">pengambilan foto yang benar</p>
      </div>
      <div class="img-example__item">
        <img class="img-example__img big" :src="require('./assets/head02.png')" />
        <p class="img-example__desc">lepaskan kacamata</p>
      </div>
      <div class="img-example__item">
        <img class="img-example__img big" :src="require('./assets/head03.png')" />
        <p class="img-example__desc">lepaskan topi</p>
      </div>
      <div class="img-example__item">
        <img class="img-example__img big" :src="require('./assets/head04.png')" />
        <p class="img-example__desc">cahaya tidak boleh terlalu gelap</p>
      </div>
    </div>
    <InButton class="next-button" @click="handleConfirmClick">Verifikasi Sekarang</InButton>
  </div>
</template>

<script>
import { InButton } from '@/common/components/InButton'

export default {
  name: 'LiveTutorial',

  components: {
    InButton
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {
    handleConfirmClick() {
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
.live-tutorial {
  &__tip {
    margin-top: 14px;
    font-family: OpenSans-SemiBold, OpenSans;
    font-size: 11px;
    line-height: 16px;
    text-align: center;
    letter-spacing: 0;
    color: #00903b;
    padding: 0 30px;
  }
}

.img-live-view {
  display: block;
  width: 196px;
  height: 196px;
  margin: 0 auto;
  margin-top: 30px;
}

.img-example {
  margin-top: 32px;
  display: flex;
  padding-left: 20px;
  padding-right: 20px;
  justify-content: space-between;

  &__item {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    &.correct {
      .img-example__img {
        width: 76px;
        height: 80px;
      }

      .img-example__desc {
        color: #00903b;
      }
    }
  }

  &__img {
    display: block;
    width: 51px;
    height: 54px;
    margin: 0 auto;
  }

  &__desc {
    font-family: OpenSans-Regular, OpenSans;
    font-size: 11px;
    font-weight: normal;
    line-height: 16px;
    text-align: center;
    letter-spacing: 0;
    color: #666;
    width: 70px;
    height: 48px;
  }
}

.next-button {
  margin: 0 auto;
  margin-top: 30px;
  width: 320px;
}
</style>
