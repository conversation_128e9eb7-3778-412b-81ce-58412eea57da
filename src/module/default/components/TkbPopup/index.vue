<template>
  <transition :name="visible ? 'in-transition--jump-down' : 'in-transition--jump-up'">
    <div v-if="visible" v-click-outside="hide" class="in-tooltip" :style="sty">
      <div v-for="(item, index) in list" :key="index" class="list-item">
        <div>{{ item }}</div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'TkbPopup',

  props: {
    value: {
      type: Boolean,
      value: false
    },
    top: {
      type: String,
      default: '0'
    },
    left: {
      type: String,
      default: '0'
    },
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  emits: ['hide'],

  data() {
    return {}
  },

  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        if (!val) {
          this.$emit('hide')
        }
      }
    },

    sty() {
      return {
        top: this.top,
        left: this.left
      }
    }
  },

  watch: {},

  methods: {
    hide(e) {
      console.log(e)
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
//@import '~@/styles/transition.less';
@import '@/common/styles/transition.scss';

@b: 3.6vw;

.in-tooltip {
  position: absolute;
  z-index: 9;
  width: 119px;
  height: 105px;
  background: url('./assets/tkb_popup.png');
  background-repeat: no-repeat;
  background-size: cover;
  .list-item {
    font-size: 10px;
    font-family: OpenSans;
    color: #2a5e3f;
    text-align: left;
    margin-top: 3px;
    margin-left: 22px;

    &:first-child {
      margin-top: 25px;
    }
  }
}
</style>
