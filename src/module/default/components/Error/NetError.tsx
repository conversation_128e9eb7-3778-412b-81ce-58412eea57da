import { InButton } from '@/common/components/InButton'
import { defineComponent } from 'vue'
import css from './NetError.module.scss'
import icon from './<EMAIL>'
export default defineComponent({
  props: {
    icon: {
      type: String,
      default: null
    },
    message: {
      type: String,
      default: 'Gagal memuat jaringan...'
    },
    retryHandler: {
      type: Function,
      default: null
    }
  },
  setup(props) {
    return () => (
      <div class={css.neterror__box}>
        <img src={props.icon ? props.icon : icon} class={css.box__img}></img>
        <span class={css.box__info}>{props.message}</span>
        <InButton class={css.box__btn} onClick={props.retryHandler}>
          Coba <PERSON>
        </InButton>
      </div>
    )
  }
})
