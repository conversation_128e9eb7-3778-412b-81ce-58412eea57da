import Vue from 'vue'
import NetError from './NetError'

const useNetErrorPage = () => {
  let app: Vue | null
  const NetErrorBoxDismiss = () => {
    if (app) {
      app.$destroy()
      app.$el.remove()
      app = null
    }
  }
  const NetErrorBoxShow = (retryHandler: () => void, idName?: string) => {
    console.log(app)
    if (!app) {
      const div = document.createElement('div')
      let fa = document.body
      if (idName) {
        const element = document.getElementById(idName)
        if (element) {
          fa = element
        }
      }
      fa.appendChild(div)
      app = new Vue({
        el: div,
        name: 'NetErrorBox',
        render(h) {
          return h(NetError, { props: { retryHandler } })
        }
      })
      app.$mount(div)
    }
  }
  return { NetErrorBoxShow, NetErrorBoxDismiss }
}

export default useNetErrorPage
