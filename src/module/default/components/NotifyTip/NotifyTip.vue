<template>
  <div class="notify-tip">
    <p class="notify-tip__text">Buka notifikasi untuk dapatkan update terbaru</p>
    <div class="notify-tip__btn" @click="handleOpenClick">Buka</div>
  </div>
</template>

<script>
export default {
  name: 'NotifyTip',

  components: {},

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {
    handleOpenClick() {
      this.$bridge.openNotificationSetting()
    }
  }
}
</script>

<style lang="scss" scoped>
.notify-tip {
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: #ecfbe7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;

  &__text {
    font-size: 12px;
    font-family: OpenSans-Regular, OpenSans, serif;
    color: #00903b;
    line-height: 16px;
  }

  &__btn {
    width: 50px;
    height: 20px;
    line-height: 20px;
    background: #00903b;
    border-radius: 22px;
    font-size: 12px;
    font-family: OpenSans-Regular, OpenSans, serif;
    color: #fff;
    text-align: center;
  }
}
</style>
