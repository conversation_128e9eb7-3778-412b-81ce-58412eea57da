<template>
  <div>
    <img class="img-fail" :src="require('./assets/fail.png')" />
    <p class="main-tip">V<PERSON><PERSON><PERSON><PERSON> wajah gagal</p>
    <p class="sub-tip"><PERSON><PERSON>an ikuti petunjuk dan ulang kembali</p>
    <InButton class="next-button" @click="handleConfirmClick">Coba lagi</InButton>
  </div>
</template>

<script>
import { InButton } from '@/common/components/InButton'

export default {
  name: 'LiveResult',

  components: {
    InButton
  },

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {
    handleConfirmClick() {
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
.img-fail {
  display: block;
  margin: 0 auto;
  margin-top: 70px;
  width: 140px;
  height: 140px;
}
.main-tip {
  margin-top: 16px;
  font-family: OpenSans-SemiBold, OpenSans;
  font-size: 18px;
  line-height: 22px;
  text-align: center;
  letter-spacing: 0px;
  color: #333333;
}
.sub-tip {
  margin-top: 8px;
  font-family: OpenSans-Regular, OpenSans;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0px;
  color: #999999;
}
.next-button {
  margin: 0 auto;
  margin-top: 30px;
  width: 200px;
}
</style>
