<template>
  <div class="page-footer">
    <p>Powered by</p>
    <img :src="require('./assets/text-logo.png')" />
  </div>
</template>

<script>
export default {
  name: 'PageFooter',

  components: {},

  data() {
    return {}
  },

  computed: {},

  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.page-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  p {
    font-family: OpenSans-SemiBold, OpenSans;
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0px;
    color: #333333;
  }
  img {
    display: block;
    width: 96px;
    height: 24px;
    margin-left: 11px;
  }
}
</style>
