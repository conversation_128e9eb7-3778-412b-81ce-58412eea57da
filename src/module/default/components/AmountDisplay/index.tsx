import { computed, defineComponent } from 'vue'
import css from './index.module.scss'
export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    },
    info: {
      type: String,
      default: ''
    },
    tips: {
      type: String,
      default: null
    },
    rightIcon: {
      type: String,
      default: ''
    },
    showLine: {
      type: Boolean,
      default: false
    },
    clickHandler: {
      type: Function,
      default: () => {}
    }
  },
  setup(props) {
    const icon = computed(() => {
      return props.rightIcon
    })

    return () => {
      return (
        <div class={css['amount-display__box']}>
          <div class={css.box__left}>
            <span class={css.box__title}>{props.title}</span>
            {props.tips ? <span class={css.box__tip}>{props.tips}</span> : ''}
          </div>

          <div class={css.box__right} onClick={() => props.clickHandler()}>
            <span class={css.box__info}>{props.info}</span>
            {props.rightIcon ? <img src={icon.value} class={css.box__img}></img> : ''}
          </div>

          {props.showLine ? <div class={css.box__line}></div> : null}
        </div>
      )
    }
  }
})
