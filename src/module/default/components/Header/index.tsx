import { defineComponent } from 'vue'
import backBlackUrl from '@/module/default/assets/back_ic.png'
import css from './index.module.scss'
import { useRoute } from 'vue-router/composables'
import { useBridge } from '@/common/bridge'

export default defineComponent({
  props: {
    needBottomBorder: {
      type: Boolean,
      default: false
    },
    backNumber: {
      type: Number,
      default: -1
    },
    title: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const route = useRoute()
    const bridge = useBridge()

    const goBack = () => {
      console.log('current page:', route.meta?.screenName)
      if (props.backNumber === -1) {
        bridge.close()
      } else {
        bridge.back(props.backNumber)
      }
    }

    bridge.dismissBackButton()

    return () => {
      return (
        <div class={css.headerBox}>
          <p class={css.statusBar}></p>
          <div
            class={[css.header, props.needBottomBorder ? css.border : css.noBorder]}
            style={{ height: '56px' }}
          >
            <img class={css.leftIcon} src={backBlackUrl} alt="back icon" onClick={goBack} />
            <div class={css.title}>{props.title}</div>
            <p></p>
          </div>
        </div>
      )
    }
  }
})
