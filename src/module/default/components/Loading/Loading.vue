<template>
  <div class="root">
    <img src="./assets/loading.png" />
  </div>
</template>

<script>
export default {
  name: 'LoadingView',

  components: {},

  data() {
    return {}
  },

  created() {}
}
</script>

<style scoped>
.root {
  display: flex;
  justify-content: center;
}

img {
  width: 28px;
  height: 28px;
  margin-top: 70%;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}
</style>
