.page {
  width: 100vw;
  min-height: 100vh;
  position: relative;
  background-color: #F7F7F7;
}

.list {
  border-radius: 12px;
  background-color: #fff;
  margin: 12px;
}

.item {
  padding: 16px 0;
  margin: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #EEEEEE;

  .text {
    font-family: Opensans, serif;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    color: #1A1A1A;
  }

  .arrow {
    width: 14px;
    height: 14px;
  }

  &:last-child {
    border-bottom: none;
  }
}

.version {
  width: 100%;
  text-align: center;
  font-family: Opensans, serif;
  font-size: 14px;
  line-height: 17px;
  color: #999999;
  position: absolute;
  bottom: 40px;
}