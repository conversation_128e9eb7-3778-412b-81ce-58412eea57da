import { InPopup } from '@/common/components/InDialog'
import { defineComponent, ref } from 'vue'

import css from './ExitPopup.module.scss'

export default defineComponent({
  name: 'ExitPopup',
  emits: ['hide', 'ok'],
  setup(props, { expose, emit }) {
    const popupRef = ref<any>(null)
    const show = () => {
      popupRef.value.show()
    }

    const hide = () => {
      popupRef.value.hide()
    }

    const handleHide = () => {
      popupRef.value.visible = false
      setTimeout(() => {
        emit('hide')
      }, 300)
    }

    const quitHanlder = () => {
      handleHide()
      emit('ok')
    }

    expose({
      show,
      hide
    })

    return () => (
      <InPopup ref={popupRef} disableMaskClose={true} on-hide={handleHide}>
        <div class={css.content}>
          <p class={css.item} onClick={quitHanlder}>
            Keluar
          </p>
          <p class={css.item} onClick={handleHide}>
            Cancel
          </p>
        </div>
      </InPopup>
    )
  }
})
