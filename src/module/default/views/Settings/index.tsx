import { defineComponent } from 'vue'
import { useBridge } from '@/common/bridge'
import Header from '@/module/default/components/Header'
import ExitPopup from './components/ExitPopup'
import { createDialog } from '@/common/components/InDialog'
import { fetchAccountLogout } from '@/module/default/api'
import useUserInfoStore from '@/module/default/store/modules/userInfo'
import useNativeInfoStore from '@/module/default/store/modules/nativeInfo'

import enterUrl from './assets/enter.png'
import css from './index.module.scss'
import { InToast } from '@/common/components/InToast'
import { userInfo } from 'os'

type IList = {
  show: boolean
  text: string
  clickHandler: () => void
}[]

export default defineComponent({
  name: 'SettingsPage',
  setup() {
    const bridge = useBridge()
    const userInfoStore = useUserInfoStore()
    const nativeInfoStore = useNativeInfoStore()
    const list: IList = [
      {
        show: true,
        text: 'Ha<PERSON> Akun',
        clickHandler: () => {
          bridge.link({ name: 'CancelAccountNotice' })
        }
      },
      {
        show: true,
        text: 'Kelu<PERSON>',
        clickHandler: () => {
          createDialog({
            component: ExitPopup,
            parent: this,
            className: '',
            class: '',
            style: '',
            root: ''
          }).onOk(async () => {
            const res = await fetchAccountLogout()
            if (res.result !== 0) {
              InToast.info(res.resultMessage)
              return
            }

            userInfoStore.clearInfo()

            bridge.back(-1, { isCancelAccount: true })
          })
        }
      }
    ]

    bridge.dismissBackButton()

    return () => (
      <div class={css.page}>
        <Header title="Pengaturan"></Header>
        <div class={css.list}>
          {list.map((item) => {
            return item.show ? (
              <div class={css.item} onClick={item.clickHandler}>
                <span class={css.text}>{item.text}</span>
                <div>
                  <img class={css.arrow} src={enterUrl} alt="arrow" />
                </div>
              </div>
            ) : null
          })}
        </div>

        <div class={css.version}>V{nativeInfoStore.appInfo.version}</div>
      </div>
    )
  }
})
