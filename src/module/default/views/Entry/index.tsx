import { defineComponent, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router/composables'

import css from './index.module.scss'

export default defineComponent({
  name: 'EntryPage',
  setup() {
    const router = useRouter()

    onMounted(() => {
      console.log('跳转页面到Home页面')
      router.replace({ name: 'Home' })
    })

    return () => {
      return <div class={css.page}></div>
    }
  }
})
