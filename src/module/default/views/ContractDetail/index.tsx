import { computed, defineComponent } from 'vue'
import { useBridge } from '@/common/bridge'
import Header from '@/module/default/components/Header'

import css from './index.module.scss'
import { useRoute, useRouter } from 'vue-router/composables'

export default defineComponent({
  name: 'ContractDetail',
  setup(props) {
    const bridge = useBridge()
    const router = useRoute()

    const PDF_VIEWER = '//oldh5.adakami.id/static/html/pdf/viewer.html?ct=none&style=simple&doc='

    bridge.dismissBackButton()

    return () => (
      <div class={[css.page]}>
        <Header title={router.query.title as string}></Header>
        <iframe
          class={css.iframe}
          width="100%"
          src={(PDF_VIEWER + router.query.url) as string}
          frameborder="0"
        ></iframe>
      </div>
    )
  }
})
