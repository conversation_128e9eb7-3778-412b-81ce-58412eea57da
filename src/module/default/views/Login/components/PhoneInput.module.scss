.input_field {
  display: flex;
  width: 100%;
  min-height: 48px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  font-weight: normal;
  font-family: OpenSans;
  position: relative;
  color: #ccc;
  flex: 1 1 0%;

  .label {
    position: absolute;
    left: 12px;
    top: 12px;
    background-color: white;
    transform-origin: left top;
    transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.324s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.324s;
  }

  input {
    min-width: 0;
    width: 100%;
    height: 48px;
    box-sizing: border-box;
    background-color: transparent;
    z-index: 100;
    font-size: 16px;
    padding: 14px 0px 14px 0px;
    padding-left: 10px;
    color: #333;
  }

  .clear_icon {
    width: 22px;
    height: 22px;
    margin-right: 7px;
  }

  .clear_icon_hide {
    display: none;
  }
}

.focus {
  border-color: #068BFD;
  color: #068BFD;

  .label {
    transform: translateY(-100%) scale(0.75);
    transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.396s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.324s;
  }
}

.input_error {
  border-color: #FF5151;

  .label {
    color: #FF5151;
  }
}

.input_disable {
  border-color: #888;

  .label {
    color: #888;
  }

  input {
    color: #888;
  }

  // hack: ios will make it lighter, so i set a darker color
  .input_ios {
    color: black;
  }
}

.error_message {
  min-height: 20px;
  height: 20px;
  padding-top: 4px;
  padding-bottom: 4px;
  line-height: 1;
  font-size: 10px;
  color: #FF5151;
  box-sizing: border-box;
}

.append {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  margin-left: 12PX;
  width: fit-content;
}