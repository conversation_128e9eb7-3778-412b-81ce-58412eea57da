import { defineComponent, reactive, ref } from 'vue'
import delUrl from '../assets/del.png'
import css from './PhoneInput.module.scss'
import { browser } from '@/common/utils/browser'

export default defineComponent({
  name: 'PhoneInput',
  props: {
    error: {
      type: String,
      default: undefined
    },
    defValue: {
      type: String,
      default: ''
    },
    disable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['input', 'focus', 'blur'],
  setup(props, { emit }) {
    const data = reactive({
      input: props.defValue,
      focus: false
    })
    const focusTimeStamp = ref(0)

    function onFocus(e: any) {
      data.focus = true
      focusTimeStamp.value = new Date().getTime()
      e.stopPropagation()
    }

    function onBlur() {
      data.focus = false
      const duration = new Date().getTime() - focusTimeStamp.value
      emit('blur', duration)
    }

    function onChange(e: any) {
      data.input = e.target.value
      emit('input', e.target.value)
    }

    function onClear() {
      data.input = ''
      emit('input', '')
    }

    return () => {
      return (
        <div>
          <div
            class={[
              css.input_field,
              data.focus || data.input.length > 0 ? css.focus : '',
              props.error && css.input_error,
              props.disable && css.input_disable
            ]}
          >
            <input
              class={browser.iOS ? css.input_ios : ''}
              type="tel"
              name="phone_number"
              value={data.input}
              onInput={onChange}
              onFocus={onFocus}
              onBlur={onBlur}
              minlength={10}
              maxlength={13}
              required
              autocomplete="off"
              disabled={props.disable}
            />
            <label class={css.label}>Nomor Handphone</label>
            <div class={css.append}>
              <img
                class={
                  data.input.length > 1 && !props.error && !props.disable
                    ? css.clear_icon
                    : css.clear_icon_hide
                }
                src={delUrl}
                alt="clear icon"
                onClick={onClear}
              />
            </div>
          </div>
          <div class={css.error_message}>{props.error}</div>
        </div>
      )
    }
  }
})
