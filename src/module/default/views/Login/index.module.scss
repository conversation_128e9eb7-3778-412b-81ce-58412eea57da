.page {
    width: 100vw;
    height: 100vh;
    // position: relative;
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

.header {
    width: 100vw;
    padding: 0 16px;
    z-index: 1;
    margin-bottom: 36px;

    .statusBar {
        height: var(--statusBarHeight);
        width: 100%;
    }

    .headerContent {
        width: 100%;
        height: 52px;
    }

    img {
        margin-top: 14px;
        width: 24px;
        height: 24px;
    }
}


.content {
    padding: 0 16px;
    flex: 1;

    .logo {
        opacity: 0.9;
        font-family: Opensans, serif;
        font-size: 24px;
        font-weight: 600;
        color: #333333;
    }

    .desc {
        margin-top: 11px;
        font-family: Opensans, serif;
        font-size: 12px;
        line-height: 18px;
        color: #646464;
    }

    .loginDesc {
        margin-top: 8px;
        margin-bottom: 16px;
        font-family: Opensans, serif;
        font-size: 12px;
        line-height: 18px;
        color: #CCCCCC;
    }
}

.login {
    background-color: #068BFD;
    width: 328px;
    height: 46px;
    border-radius: 8px;
    font-family: Opensans, serif;
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    text-align: center;
    color: #FFFFFF;
}

.loginAuth {
    margin-top: 12px;
    width: 328px;
    height: 46px;
    border-radius: 8px;
    background: #00903B;
    display: flex;
    justify-content: center;
    align-items: center;

    .ak {
        width: 20px;
        height: 20px;
        margin-right: 5px;
    }

    span {
        font-family: Opensans, serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 18px;
        color: #FFFFFF;
    }
}

.terms {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;

    font-family: Opensans, serif;
    font-size: 12px;
    line-height: 16px;
    color: #545454;

    .termsLink {
        margin-left: 3px;
        font-weight: 600;
        color: #068BFD
    }
}

.bottom {
    width: 100vw;
    height: 72px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
        width: 277.5px;
        height: 30px;
    }
}