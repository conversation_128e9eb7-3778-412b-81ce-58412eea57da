import { useBridge } from '@/common/bridge'
import { defineComponent, reactive, computed, ref } from 'vue'
import PhoneInput from './components/PhoneInput'
import { InToast } from '@/common/components/InToast'
import InCheckbox from '@/common/components/InForm/InCheckbox/InCheckbox.vue'
import useUserInfo from '@/module/default/store/modules/userInfo'
import { fetchUnionLogin } from '@/module/default/api'
import { trackSAClick, trackSAInput } from '@/common/track/trackSA'
import { useRoute } from 'vue-router/composables'

import css from './index.module.scss'
import backUrl from './assets/back.png'
import akUrl from './assets/ak.png'
import bottomUrl from './assets/bottom.png'

const phoneRegex = /((^08)(\d{8,11})$)/

export default defineComponent({
  name: 'LoginPage',
  setup() {
    const bridge = useBridge()
    const route = useRoute()
    const store = useUserInfo()
    const data = reactive({
      phoneNumber: ''
    })
    const agreeToAgreement = ref(true)
    const changeAgreeToAgreementHandler = () => {
      agreeToAgreement.value = !agreeToAgreement.value
    }

    function backHandler() {
      trackSAClick({
        tgt_event_id: 'cashcerdas_inputPhone_back',
        tgt_name: 'cashcerdas-手机号输入页-返回',
        page: route.meta?.screenName,
        params: [],
        extraParams: {}
      })

      bridge.close()
    }

    const errorMsg = computed(() => {
      if (data.phoneNumber.length === 0) {
        return ''
      }

      if (!phoneRegex.test(data.phoneNumber)) {
        return 'Silakan masukkan nomor ponsel yang valid (08), dengan 10-13 digit.'
      }

      return ''
    })

    const inputPhoneHandler = (e: string) => {
      data.phoneNumber = e
    }

    const loginHandler = async () => {
      if (!agreeToAgreement.value) {
        InToast.info(
          'Pinjaman tidak bisa dilanjutkan tanpa mencentang perjanjian. Mohon centang kembali untuk melanjutkan.'
        )
        return
      }

      if (data.phoneNumber.length === 0) {
        InToast.info('Silakan masukkan nomor HP')
        return
      }

      if (errorMsg.value.length !== 0) {
        InToast.info(errorMsg.value)
        return
      }

      trackSAClick({
        tgt_event_id: 'cashcerdas_inputPhone_submit',
        tgt_name: 'cashcerdas-手机号输入页-提交按钮',
        page: route.meta?.screenName,
        params: [data.phoneNumber],
        extraParams: {}
      })

      bridge.link({
        name: 'ApplyOTP',
        query: {
          phoneNumber: data.phoneNumber
        }
      })
    }

    const loginAuthHandler = async () => {
      if (!agreeToAgreement.value) {
        InToast.info(
          'Pinjaman tidak bisa dilanjutkan tanpa mencentang perjanjian. Mohon centang kembali untuk melanjutkan.'
        )
        return
      }
      const loginByAdakamiRes = await bridge.loginByAdakami()
      const externalToken = loginByAdakamiRes.authCode
      if (!externalToken) {
        InToast.info('登录失败')
        return
      }

      const res = await fetchUnionLogin({ externalToken: externalToken })
      if (res.result !== 0) {
        InToast.info(res.resultMessage)
        return
      }

      const openId = res.content?.openId as string
      const userName = res.content?.userName as string
      const phoneNuber = res.content?.mobile as string
      const token = res.content?.token as string
      store.saveInfo(openId, userName, phoneNuber, token)

      bridge.back(-1)
    }

    const gotoPrivacyContract = () => {
      bridge.link({
        name: 'ContractDetail',
        query: {
          title: 'Perjanjian privasi',
          url: 'https://bucket-in-static-res.oss-ap-southeast-5.aliyuncs.com/pdf/Privacy%20Policy_CashCerdas_Bahasa%2020250114.pdf'
        }
      })
    }

    const gotoShareDataContract = () => {
      bridge.link({
        name: 'ContractDetail',
        query: {
          title: 'Kententuan',
          url: 'https://bucket-in-static-res.oss-ap-southeast-5.aliyuncs.com/pdf/Terms%20and%20Conditions%20For%20TCF%2020250216.pdf'
        }
      })
    }

    const handlePhoneBlur = (duration: number) => {
      trackSAInput({
        tgt_event_id: 'cashcerdas_phone_input_time',
        tgt_name: 'cashcerdas-手机号输入时间',
        page: route.meta?.screenName,
        params: [data.phoneNumber, duration + ''],
        extraParams: {}
      })
    }

    bridge.dismissBackButton()

    return () => {
      return (
        <div class={css.page}>
          <div class={css.header}>
            <p class={css.statusBar}></p>
            <img src={backUrl} alt="back" onClick={backHandler}></img>
          </div>
          <div class={css.content}>
            <div class={css.logo}>CashCerdas</div>
            <p class={css.desc}>Login untuk cek limit eksklusif Anda, dan mulai eksplorasi!</p>
            <p class={css.loginDesc}>
              Dengan mendaftar, saya menyetujui PT Teknologi Cerdas Finansial untuk mengumpulkan data
              informasi saya dari PT Pembiayaan Digital Indonesia berdasarkan kerja sama antara para pihak dan
              telah dilengkapi ketentuan terkait dengan perlindungan data pribadi.
            </p>

            <PhoneInput
              on-input={inputPhoneHandler}
              defValue={data.phoneNumber}
              error={errorMsg.value}
              on-blur={handlePhoneBlur}
            ></PhoneInput>

            <div class={css.terms}>
              <InCheckbox
                value={agreeToAgreement}
                on-input={changeAgreeToAgreementHandler}
                width={14}
                height={14}
                svgWidth={7}
                svgHeight={7}
              ></InCheckbox>
              <p>
                Saya telah baca dan menyetujui{' '}
                <span onClick={gotoShareDataContract} class={css.termsLink}>
                  kententuan
                </span>{' '}
                dan{' '}
                <span onClick={gotoPrivacyContract} class={css.termsLink}>
                  perjanjian privasi
                </span>
              </p>
            </div>

            <button class={css.login} onClick={loginHandler}>
              Daftar/Login
            </button>
            {/* <button class={css.loginAuth} onClick={loginAuthHandler}>
              <img class={css.ak} src={akUrl} alt="ak" />
              <span>Otorisasi Login</span>
            </button> */}
          </div>

          <div class={css.bottom}>
            <img src={bottomUrl} alt="bottom" />
          </div>
        </div>
      )
    }
  }
})
