.page {
  width: 100vw;
  min-height: 100vh;
  position: relative;
  background-color: #F7F7F7;
}

.item {
  margin: 12px 12px 0 12px;
  height: 60px;
  width: 336px;
  padding: 0 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  background-color: #fff;

  .leftSide {
    display: flex;
    align-items: center;

    .icon {
      width: 20px;
      height: 20px;
      margin-right: 6px;
    }

    .text {
      font-family: Opensans, serif;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      color: #333333;
    }
  }

  .arrow {
    width: 14px;
    height: 14px;
  }
}