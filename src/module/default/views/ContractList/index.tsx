import { defineComponent } from 'vue'
import { useBridge } from '@/common/bridge'
import Header from '@/module/default/components/Header'

import contractUrl from './assets/contract.png'
import enterUrl from './assets/enter.png'
import css from './index.module.scss'

type IList = {
  show: boolean
  image: string
  text: string
  clickHandler: () => void
}[]

export default defineComponent({
  name: 'ContractListPage',
  setup() {
    const bridge = useBridge()
    const list: IList = [
      // {
      //   show: true,
      //   image: contractUrl,
      //   text: 'Perjanjian privasi',
      //   clickHandler: () => {
      //     bridge.link({
      //       name: 'ContractDetail',
      //       query: {
      //         title: 'Perjanjian privasi',
      //         url: 'https://bucket-in-static-res.oss-ap-southeast-5.aliyuncs.com/pdf/Privacy%20Policy_CashCerdas_Bahasa%2020250114.pdf'
      //       }
      //     })
      //   }
      // },
      {
        show: true,
        image: contractUrl,
        text: 'Kent<PERSON><PERSON>',
        clickHandler: () => {
          bridge.link({
            name: 'ContractDetail',
            query: {
              title: 'Kententuan',
              url: 'https://bucket-in-static-res.oss-ap-southeast-5.aliyuncs.com/pdf/Terms%20and%20Conditions%20For%20TCF%2020250216.pdf'
            }
          })
        }
      }
    ]

    return () => (
      <div class={css.page}>
        <Header title="Perjanjian"></Header>
        {list.map((item) => {
          return item.show ? (
            <div class={css.item} onClick={item.clickHandler}>
              <div class={css.leftSide}>
                <img class={css.icon} src={item.image} alt="any" />
                <span class={css.text}>{item.text}</span>
              </div>
              <div>
                <img class={css.arrow} src={enterUrl} alt="arrow" />
              </div>
            </div>
          ) : null
        })}
      </div>
    )
  }
})
