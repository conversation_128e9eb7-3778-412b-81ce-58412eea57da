.page {
  width: 100vw;
  min-height: 100vh;
  position: relative;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;

  .img {
    height: 54px;
    width: 54px;
    margin-top: 26px;
  }

  .title {
    margin-top: 34px;
    font-family: <PERSON>sans, serif;
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
    text-align: center;
    color: #333333;
  }

  .desc {
    margin-top: 25px;
    font-family: Opensans, serif;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    color: #333333;
  }
}


.headerBox {
  z-index: 999;
  background-color: #fff;
  position: sticky;
  top: 0;

  .statusBar {
    height: var(--statusBarHeight);
    width: 100%;
  }
}

.header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.border {
  border-bottom: 1px solid #EEEEEE;
}

.noNorder {
  border: none;
}

.leftIcon {
  width: 24px;
  height: 24px;
}

.headerTitle {
  font-family: Opensans, serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 22px;
  text-align: center;
  color: #333333;
}

.right_icon_container {
  padding-right: 12px;
  width: 24px;
  height: 24px;
}