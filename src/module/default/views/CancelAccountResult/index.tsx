import { defineComponent, ref, onMounted, computed } from 'vue'
import { useBridge } from '@/common/bridge'
import { useRoute } from 'vue-router/composables'

import css from './index.module.scss'
import failUrl from './assets/fail.png'
import successUrl from './assets/success.png'
import backBlackUrl from '@/module/default/assets/back_ic.png'

export default defineComponent({
  name: 'CancelAccountResultPage',
  setup() {
    const route = useRoute()
    const bridge = useBridge()
    const isSuccess = ref<boolean>(route.query.isSuccess === 'true')
    const url = computed(() => {
      return isSuccess.value ? successUrl : failUrl
    })
    const title = computed(() => {
      return isSuccess.value ? 'Akun Anda telah dihapuskan' : 'Penghapusan Akun Gagal'
    })
    const desc = computed(() => {
      return isSuccess.value
        ? 'Semua data Anda telah dihapus secara permanen. Terima kasih telah bersama kami.'
        : 'Penutupan akun tidak dapat dilakukan karena pinjaman yang belum terbayar. Silakan lunasi pinjaman Anda untuk menghapus akun.'
    })

    const goBack = () => {
      bridge.back(-4, { isCancelAccount: true })
    }

    onMounted(() => {
      console.log('query', route.query)
    })

    bridge.dismissBackButton()

    return () => (
      <div class={css.page}>
        <div class={css.headerBox}>
          <p class={css.statusBar}></p>
          <div class={[css.header]} style={{ height: '56px' }}>
            <img class={css.leftIcon} src={backBlackUrl} alt="back icon" onClick={goBack} />
            <div class={css.headerTitle}>Hapus Akun</div>
            <p></p>
          </div>
        </div>
        <div class={css.content}>
          <img class={css.img} src={url.value} alt="" />
          <p class={css.title}>{title.value}</p>
          <p class={css.desc}>{desc.value}</p>
        </div>
      </div>
    )
  }
})
