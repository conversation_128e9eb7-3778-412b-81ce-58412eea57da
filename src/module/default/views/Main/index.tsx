import { defineComponent, reactive, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router/composables'

import homeOnPng from './assets/home_on.png'
import homeOffPng from './assets/home_off.png'
import meOnPng from './assets/me_on.png'
import meOffPng from './assets/me_off.png'

import css from './index.module.scss'
import { useBridge } from '@/common/bridge'

export default defineComponent({
  name: 'MainCop',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const bridge = useBridge()

    const data = reactive({
      tabIndex: 1
    })
    const homeUrl = computed(() => {
      return data.tabIndex === 1 ? homeOnPng : homeOffPng
    })
    const meUrl = computed(() => {
      return data.tabIndex === 2 ? meOnPng : meOffPng
    })

    const onChange = (index: number) => {
      if (index === data.tabIndex) return
      switch (index) {
        case 1:
          router.replace({ name: 'Home' })
          break
        case 2:
          router.replace({ name: 'Me' })
          break
      }
    }

    watch(
      () => route.path,
      (newPath, oldPath) => {
        if (newPath === '/main/home') {
          data.tabIndex = 1
        } else if (newPath === '/main/me') {
          data.tabIndex = 2
        } else {
          data.tabIndex = 1
        }
        console.log(`路由从 ${oldPath} 变更为 ${newPath}`)
      },
      {
        immediate: true
      }
    )

    bridge.onShow((data: any) => {
      if (data?.isCancelAccount && route.path !== '/main/home') {
        router.replace({ name: 'Home' })
      }
    })

    return () => {
      return (
        <div class={css.box}>
          <div id="scrollView" class={css.content}>
            <keep-alive>
              <router-view></router-view>
            </keep-alive>
          </div>

          <div class={css.tabBar}>
            <div onClick={() => onChange(1)}>
              <img src={homeUrl.value} />
              <span class={data.tabIndex === 1 ? css.span_active : css.span}>Beranda</span>
            </div>
            <div onClick={() => onChange(2)}>
              <img src={meUrl.value} />
              <span class={data.tabIndex === 2 ? css.span_active : css.span}>Akun Saya</span>
            </div>
          </div>
        </div>
      )
    }
  }
})
