import { computed, defineComponent, onMounted, onBeforeUnmount } from 'vue'
import { useBridge } from '@/common/bridge'
import Header from '@/module/default/components/Header'

import css from './index.module.scss'
import { useRoute } from 'vue-router/composables'
import { trackSAClick } from '@/common/track/trackSA'
import useUserInfoStore from '../../store/modules/userInfo'

export default defineComponent({
  name: 'ChannelDetail',
  setup(props) {
    const bridge = useBridge()
    const route = useRoute()
    const store = useUserInfoStore()

    const url = computed(() => {
      if (window.location.href.indexOf('fat-') > -1 || window.location.href.indexOf(':5173') > -1) {
        return `https://fat-h5.cashcerdas.id/detail?loanChannelId=${route.query?.channelId}&loanChannelName=${route.query?.channelName}&clickEntry=p2pList&source=3&gaid=${route.query?.gaid}`
      } else {
        return `https://h5.cashcerdas.id/detail?loanChannelId=${route.query?.channelId}&loanChannelName=${route.query?.channelName}&clickEntry=p2pList&source=3&gaid=${route.query?.gaid}`
      }
    })

    const trackDownloadClk = (event: any) => {
      if (event.data.action === 'trackDownloadClk') {
        trackSAClick({
          tgt_event_id: 'cashcerdas_loanstore_product_download_click',
          tgt_name: 'cashcerdas-贷超产品下载点击',
          page: route.meta?.screenName,
          params: [
            store.getMobile ? store.getMobile : '',
            route.query?.source + '',
            route.query?.channelName + '',
            route.query?.cardType === '6' ? '戳额被拒' : '其他'
          ]
        })
      }
    }

    onMounted(() => {
      window.addEventListener('message', trackDownloadClk)
    })

    onBeforeUnmount(() => {
      window.removeEventListener('message', trackDownloadClk)
    })

    bridge.dismissBackButton()

    return () => (
      <div class={css.page}>
        <Header title="Saluran"></Header>
        <iframe class={css.iframe} src={url.value} frameborder="0"></iframe>
      </div>
    )
  }
})
