.box {
    width: 100vw;
    background-color: #fff;
    height: auto;
    position: absolute;
    bottom: 0;
    left: 0;
    padding-bottom: 30px;
    border-radius: 12px 12px 0px 0px;
}

.header {
    height: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 20px 0 20px;

    .title {
        font-family: Opensans, serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;
        color: #333333;
    }

    .close {
        width: 16px;
        height: 16px;
    }
}

.content {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    margin: 20px 20px 0 20px;

    .time {
        font-family: Opensans, serif;
        font-size: 12px;
        line-height: 18px;
        color: #ADADAD;
    }

    .leftSide {
        p {
            font-family: Opensans, serif;
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
            color: #333333;
            margin-bottom: 2px;
        }


    }

    .connect {
        font-family: Opensans, serif;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #068BFD;
    }


    .row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;

        p {
            font-family: Opensans, serif;
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
            color: #333333;
            margin-bottom: 2px;
        }

        span {
            font-family: Opensans, serif;
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
            color: #068BFD;
        }

        .tip {
            font-family: Opensans, serif;
            font-size: 12px;
            line-height: 18px;
            color: #ADADAD;
        }
    }
}