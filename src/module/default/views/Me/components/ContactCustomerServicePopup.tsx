import { InPopup } from '@/common/components/InDialog'
import { defineComponent, ref, onMounted } from 'vue'
import closeUrl from '../assets/close.png'

import css from './ContactCustomerServicePopup.module.scss'
import { copy } from '@/common/utils'
import { InToast } from '@/common/components/InToast'
import { trackSAImp } from '@/common/track/trackSA'
import useUserInfoStore from '@/module/default/store/modules/userInfo'

export default defineComponent({
  name: 'ContactCustomerServicePopup',
  props: {
    screenName: {
      type: String,
      default: ''
    }
  },
  emits: ['hide', 'ok'],
  setup(props, { expose, emit }) {
    const store = useUserInfoStore()
    const popupRef = ref<any>(null)
    const info =
      ref(`Untuk membantu menyelesaikan masalah Anda dengan lebih efisien, silakan mengirimkan email dengan melampirkan informasi berikut
</br>1. <PERSON>a leng<PERSON>p sesuai <PERSON>
</br>2. Nomor HP terdaftar
</br>3. <PERSON>mor NIK KTP
</br>4. Detail pertanyaan/kendala (screenshot kendala pada aplikasi`)
    const show = () => {
      popupRef.value.show()
    }

    const hide = () => {
      popupRef.value.hide()
    }

    const handleHide = () => {
      popupRef.value.visible = false
      setTimeout(() => {
        emit('hide')
      }, 300)
    }

    const copyHandler = () => {
      copy('<EMAIL>')
      InToast.info('Berhasil disalin')
    }

    onMounted(() => {
      trackSAImp({
        tgt_event_id: 'cashcerdas_selfinfo_phone_email_imp',
        tgt_name: 'cashcerdas-个人中心页-电话邮箱弹窗曝光',
        page: props.screenName,
        params: [store.mobile],
        extraParams: {}
      })
    })

    expose({
      show,
      hide
    })

    return () => (
      <InPopup ref={popupRef} disableMaskClose={true} on-hide={handleHide}>
        <div class={css.box}>
          <div class={css.header}>
            <p class={css.title}>Hubungi CS</p>
            <img class={css.close} src={closeUrl} alt="close" onClick={handleHide} />
          </div>
          <div class={css.content}>
            <div class={css.row}>
              <p><EMAIL></p>
              <span onClick={copyHandler}>Menyalin</span>
            </div>
            <div class={css.row}>
              <p class={css.tip} domProps-InnerHTML={info.value} />
            </div>
            <div class={css.row}>
              <p>0821-2804-8915</p>
              <a href="tel:0821-2804-8915" class={css.connect}>
                Hubungi
              </a>
            </div>

            <span class={css.time}>Senin-Minggu: 08:00 - 20:00</span>
          </div>
        </div>
      </InPopup>
    )
  }
})
