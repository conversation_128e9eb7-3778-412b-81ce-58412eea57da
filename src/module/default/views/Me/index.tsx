import { defineComponent } from 'vue'
import { useBridge } from '@/common/bridge'
import { createDialog } from '@/common/components/InDialog'
import ContactCustomerServicePopup from './components/ContactCustomerServicePopup'
import useUserInfo from '@/module/default/store/modules/userInfo'
import { useRoute } from 'vue-router/composables'

import avatarUrl from './assets/avatar.png'
import enterUrl from './assets/enter.png'
import contractUrl from './assets/contract.png'
import customerServiceUrl from './assets/customerService.png'
import settingUrl from './assets/setting.png'
import customerService3xUrl from './assets/<EMAIL>'

import css from './index.module.scss'
import { trackSAClick } from '@/common/track/trackSA'

type IList = {
  name: string
  showName: boolean
  list: {
    show: boolean
    image: string
    text: string
    clickHandler: () => void
  }[]
}[]

export default defineComponent({
  name: 'MePage',
  setup() {
    const bridge = useBridge()
    const store = useUserInfo()
    const route = useRoute()

    const list: IList = [
      {
        name: 'main',
        showName: false,
        list: [
          // {
          //   show: true,
          //   image: bankcardUrl,
          //   text: 'Pengaturan Bank',
          //   clickHandler: () => {
          //     console.log('1')
          //   }
          // },
          {
            show: true,
            image: contractUrl,
            text: 'Perjanjian',
            clickHandler: () => {
              bridge.link({ name: 'ContractList' })
            }
          }
          // {
          //   show: true,
          //   image: couponUrl,
          //   text: 'Kupon',
          //   clickHandler: () => {
          //     console.log('2')
          //   }
          // }
        ]
      },
      {
        name: 'Hubungi kami',
        showName: true,
        list: [
          {
            show: true,
            image: customerServiceUrl,
            text: 'Hubungi CS',
            clickHandler: () => {
              createDialog({
                component: ContactCustomerServicePopup,
                parent: this,
                className: '',
                class: '',
                style: '',
                root: '',
                screenName: route.meta?.screenName
              })
            }
          }
        ]
      },
      {
        name: 'Other',
        showName: true,
        list: [
          {
            show: true,
            image: settingUrl,
            text: 'Pengaturan',
            clickHandler: () => {
              bridge.link({ name: 'Settings' })
            }
          }
        ]
      }
    ]

    const gotoCustomerService = () => {
      trackSAClick({
        tgt_event_id: 'cashcerdas_selfinfo_livechat_clk',
        tgt_name: 'cashcerdas-个人中心页-livechat-点击',
        page: route.meta?.screenName,
        params: [store.mobile],
        extraParams: {}
      })

      const url = `https://userim.cashcerdas.id/?channel=0&guestFlag=${store.isLogin ? 0 : 1}&bizId=${
        store.openId
      }`

      bridge.link(url)
    }

    bridge.onShow(async () => {
      store.$hydrate()
    })

    bridge.dismissBackButton()

    return () => (
      <div class={css.page}>
        <p class={css.statusBar}></p>
        <div class={css.user}>
          <div class={css.userBox}>
            <img class={css.avatar} src={avatarUrl} alt="avatar" />
            <div class={css.title}>Welcome{`, ${store.username}`}</div>
          </div>

          <img
            class={css.customerService3x}
            src={customerService3xUrl}
            alt="avatar image"
            onClick={gotoCustomerService}
          />
        </div>

        <div class={css.list}>
          {list.map((card) => {
            return (
              <div class={css.card}>
                {card.showName && <p class={css.cardName}>{card.name}</p>}
                <div class={css.cardBox}>
                  {card.list.map((item) => {
                    return item.show ? (
                      <div class={css.item} onClick={item.clickHandler}>
                        <div class={css.leftSide}>
                          <img class={css.icon} src={item.image} alt="any" />
                          <span class={css.text}>{item.text}</span>
                        </div>
                        <div>
                          <img class={css.arrow} src={enterUrl} alt="arrow" />
                        </div>
                      </div>
                    ) : null
                  })}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    )
  }
})
