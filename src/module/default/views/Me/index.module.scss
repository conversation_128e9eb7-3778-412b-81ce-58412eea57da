.page {
  width: 100vw;
  min-height: 100vh;
  position: relative;
  background-color: #F7F7F7;

  &::before {
    content: '';
    position: absolute;
    background-image: url(./assets/my_bg.png);
    width: 100vw;
    height: 325px;
    background-size: cover;
  }
}


.statusBar {
  height: var(--statusBarHeight);
  width: 100%;
}


.user {
  height: 84px;
  box-sizing: border-box;
  padding: 24px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .userBox {
    display: flex;
  }

  .avatar {
    width: 36px;
    height: 36px;
    margin-right: 16px;
  }

  .title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-family: Opensans, serif;
    color: #333333;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }

  .customerService3x {
    width: 24px;
    height: 24px;
  }
}

.list {
  padding: 0px 12px;
}


.card {
  .cardName {
    margin-top: 24px;
    margin-bottom: 10px;
    font-family: Opensans, serif;
    font-size: 14px;
    line-height: 20px;
    color: #333333;
  }

  .cardBox {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
  }
}

.item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 42px;
  padding: 0 14px 0 12px;

  .leftSide {
    display: flex;
    align-items: center;

    .icon {
      width: 24px;
      height: 24px;
    }

    .text {
      margin-left: 10px;
      font-family: Opensans, serif;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #333333;
    }
  }

  .arrow {
    width: 14px;
    height: 14px;
  }
}