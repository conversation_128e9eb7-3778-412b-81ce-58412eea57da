import { InDialog } from '@/common/components/InDialog'
import { defineComponent, ref } from 'vue'

import css from './DeleteAccountDialog.module.scss'
import tipUrl from '../assets/tip.png'

export default defineComponent({
  name: 'DeleteAccountDialog',
  emits: ['hide', 'ok'],
  setup(props, { expose, emit }) {
    const dialogRef = ref<any>(null)
    const show = () => {
      dialogRef.value.show()
    }

    const hide = () => {
      dialogRef.value.hide()
    }

    const handleHide = () => {
      dialogRef.value.visible = false
      setTimeout(() => {
        emit('hide')
      }, 300)
    }

    const confirmHandler = () => {
      handleHide()
      emit('ok')
    }

    expose({
      show,
      hide
    })

    return () => (
      <InDialog ref={dialogRef} on-hide={handleHide}>
        <div class={css.content}>
          <img class={css.tip} src={tipUrl}></img>
          <p class={css.title}>Penghapusan Akun</p>
          <p class={css.desc}>
            Penghapusan Akun Setelah permohonan penghapusan akun dikonfirmasi, CashCerdas akan segera
            memproses dan menghapus informasi akun Anda. Nomor ponsel Anda tidak akan dapat digunakan lagi di
            CashCerdas. Mohon konfirmasi apakah Anda ingin menghapus akun?
          </p>
          <button class={css.cancel} onClick={handleHide}>
            Membatalkan
          </button>
          <button class={css.confirm} onClick={confirmHandler}>
            Konfirmasi
          </button>
        </div>
      </InDialog>
    )
  }
})
