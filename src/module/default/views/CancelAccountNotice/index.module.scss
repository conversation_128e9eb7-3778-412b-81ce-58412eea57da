.page {
  width: 100vw;
  min-height: 100vh;
  position: relative;
}

.guide {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .cc {
    width: 64px;
    height: 64px;
    margin-top: 24px;
  }

  p {
    width: 269px;
    margin-top: 8px;
    font-family: Opensans, serif;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #333333;
    text-align: center;
  }
}

.desc {
  margin-top: 22px;
  padding: 0 16px;
  font-family: Opensans, serif;
  font-size: 14px;
  line-height: 20px;
  color: #333333;

  white-space: pre-wrap;
}


.protocol {
  margin: 32px 16px 0 16px;
  font-family: Opensans, serif;
  font-size: 12px;
  line-height: 17px;
  color: #999999;

  display: flex;
  align-items: center;

  .in-checkbox__bg {
    width: 14px;
    height: 14px;
  }

  a {
    color: #068BFD;
  }
}

.next {
  margin: 16px;
  width: 328px;
  height: 46px;
  border-radius: 8px;
  background: #068BFD;

  font-family: Opensans, serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 19px;
  color: #FFFFFF;

  &:disabled {
    background: #D6DFE7;
  }
}

.cancel {
  margin: 0 16px 30px 16px;
  width: 328px;
  height: 46px;
  border-radius: 8px;
  border: 1px solid #068BFD;
  background: #fff;

  font-family: Opensans, serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 19px;
  color: #068BFD;
}