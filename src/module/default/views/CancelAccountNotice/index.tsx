import { defineComponent, onMounted, ref } from 'vue'
import { useBridge } from '@/common/bridge'
import Header from '@/module/default/components/Header'
import DeleteAccountDialog from './components/DeleteAccountDialog'
import InCheckbox from '@/common/components/InForm/InCheckbox/InCheckbox.vue'
import { createDialog } from '@/common/components/InDialog'

import logoUrl from './assets/logo.png'
import css from './index.module.scss'

export default defineComponent({
  name: 'CancelAccountNoticePage',
  setup() {
    const bridge = useBridge()
    const agreeToAgreement = ref(false)
    const notice = `
Untuk menjaga keamanan data dan akun Anda serta menghindari potensi perselisihan terkait dana dan pinjaman, pastikan Anda memahami ketentuan berikut sebelum mengajukan penghapusan akun: 

1. Semua Pinjaman Telah Lunas Pastikan seluruh pinjaman Anda sudah dilunasi sepenuhnya dan tidak ada pinjaman yang aktif. Jika ada pinjaman yang belum diseles<PERSON>kan, akun Anda tidak dapat dihapus. 

2. Tidak Ada Sisa Saldo di Akun Jika masih ada saldo di akun Anda, harap cairkan terlebih dahulu. Penghapusan akun tidak dapat dilakukan jika saldo masih tersisa. 

3. Reset Hadiah, Kupon, dan Bonus Setelah akun dihapus, semua hadiah, kupon, atau bonus yang masih ada di akun akan direset dan tidak dapatdikembalikan.
`

    const changeAgreeToAgreementHandler = () => {
      agreeToAgreement.value = !agreeToAgreement.value
    }

    const nextHandler = () => {
      if (!agreeToAgreement.value) {
        return
      }

      createDialog({
        component: DeleteAccountDialog,
        parent: this,
        className: '',
        class: '',
        style: '',
        root: ''
      }).onOk(() => {
        bridge.link({ name: 'CancelAccountOtp' })
      })
    }
    const goBackHandler = () => {
      bridge.close()
    }

    onMounted(() => {
      console.log('注销账户须知')
    })

    return () => (
      <div class={css.page}>
        <Header title="Hapus Akun"></Header>
        <div class={css.guide}>
          <img class={css.cc} src={logoUrl} alt="cc" />
          <p>Panduan Penghapusan Akun CashCerdas</p>
        </div>
        <div class={css.desc}>{notice}</div>
        <div class={css.protocol}>
          <InCheckbox
            value={agreeToAgreement}
            on-input={changeAgreeToAgreementHandler}
            width={14}
            height={14}
            svgWidth={7}
            svgHeight={7}
          ></InCheckbox>
          <p>
            Saya telah membaca dan menyetujui ketentuan tersebut. Untuk informasi lebih lanjut, silakan lihat.
          </p>
        </div>

        <button class={css.next} disabled={!agreeToAgreement.value} onClick={nextHandler}>
          Lanjut
        </button>

        <button class={css.cancel} onClick={goBackHandler}>
          Saya pikir lagi
        </button>
      </div>
    )
  }
})
