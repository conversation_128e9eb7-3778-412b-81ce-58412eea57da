.page {
    width: 100vw;
    height: 100%;
    position: relative;
    background-color: #F7F7F7;
    overflow-y: scroll;
    font-family: Opensans, serif;

    &::before {
        content: '';
        position: absolute;
        background-image: url(./assets/home_bg.png);
        width: 100vw;
        height: 325px;
        background-size: cover;
    }
}

.header {
    width: 100vw;
    padding: 0 16px;
    z-index: 999;
    top: 0;
    position: sticky;
    transition: all 0.5s;


    .statusBar {
        height: var(--statusBarHeight);
        width: 100%;
    }

    .headerContent {
        width: 100%;
        height: 52px;
    }
}

.headerContent {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .logo {
        font-size: 18.18px;
        font-weight: 600;
        color: #333333;
    }

    .chat {
        width: 24px;
        height: 24px;
    }
}

.sticky {
    background-color: #fff;
}

.main {
    width: 100vw;
    padding: 0 12px;

    .tip {
        margin: 24px 0 14px 0;
        opacity: 0.899;
        font-family: Opensans, serif;
        font-size: 13px;
        font-weight: normal;
        line-height: 20px;
        color: #333333;
    }
}