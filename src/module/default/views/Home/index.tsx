import { defineComponent, onMounted, ref, onActivated, onBeforeUnmount, computed, nextTick } from 'vue'

import { createDialog } from '@/common/components/InDialog'
import { useBridge } from '@/common/bridge'
import RepaymentCard from './components/RepaymentCard'
import HomeCard from './components/HomeCard'
import P2PList from './components/P2PList'
import GuideCard from './components/GuideCard'
import UnequalUserAuthPopup from './components/UnequalUserAuthPopup'
import useUserInfo from '@/module/default/store/modules/userInfo'
import { InToast } from '@/common/components/InToast'
import LoanChannelCardList from './components/LoanChannelCardList.vue'
import NewHomeCard from './components/NewHomeCard'
import ProvideLoanAgainPopup from './components/ProvideLoanAgainPopup'
import { useRoute } from 'vue-router/composables'

import {
  HomeCardEnum,
  QuotaCardStatusEnum,
  LiveCycleCodeEnum,
  getHomeCardEnumText
} from '@/module/default/constant/homeConstant'
import { ILoanCards } from '@/module/default/api/response'
import {
  fetchPagesHomeInfo,
  fetchAdaFlxEntrance,
  savePushToken,
  popUpShowFlag,
  popUpComplete
} from '@/module/default/api'
import { fetchP2pList, fetchP2pHomeBannerItem, fetchP2pHomeBannerList } from '@/module/default/api/origin'
import { trackSAClick } from '@/common/track/trackSA'

import css from './index.module.scss'
// import chatUrl from './assets/chat.png'

export default defineComponent({
  name: 'HomePage',
  setup() {
    const route = useRoute()
    const bridge = useBridge()
    const store = useUserInfo()
    const dispAdaFlxFakeCard = ref(false)
    const maxLoanAmount = ref('')
    const loanCards = ref({} as ILoanCards)
    const p2pList = ref<fetchP2pHomeBannerItem[]>([])
    const loanChanelList = ref<fetchP2pHomeBannerItem[]>([])
    const isFirstReject = ref()
    const isStickstatusBarAndHeader = ref(false)
    const pageRef = ref<any>(null)
    let protectPopupLock = false

    const isShowGuideCard = computed(() => {
      if (!store.isLogin) {
        return true
      }

      return !!amount.value && amount.value > 0
    })
    const cardType = computed(() => {
      let res = HomeCardEnum.LOAN_AMOUNT

      if (dispAdaFlxFakeCard.value) {
        return HomeCardEnum.BEFORE_CREDIT
      }

      if (loanCards.value?.adaFlx && loanCards.value?.adaFlx.quotaCard) {
        if (loanCards.value?.adaFlx?.quotaCard?.status === QuotaCardStatusEnum.NO_QUOTA_OBTAINED) {
          return HomeCardEnum.BEFORE_CREDIT
        } else if (
          loanCards.value?.adaFlx?.quotaCard?.status === QuotaCardStatusEnum.OBTAINED_QUOTA_SUCCESSFULLY &&
          loanCards.value?.adaFlx?.quotaCard?.totalAmount === 0
        ) {
          return HomeCardEnum.INSUFFICIENT_AMOUNT
        } else if (
          loanCards.value?.adaFlx?.quotaCard?.status === QuotaCardStatusEnum.OBTAINED_QUOTA_SUCCESSFULLY &&
          loanCards.value?.adaFlx?.quotaCard?.recentExpireTime < Date.now()
        ) {
          return HomeCardEnum.CREDIT_LIMIT_EXPIRED
        } else if (loanCards.value?.adaFlx?.quotaCard?.status === QuotaCardStatusEnum.GETTING_QUOTA) {
          return HomeCardEnum.OBTAINING_QUOTA
        } else if (
          loanCards.value?.adaFlx?.quotaCard?.status === QuotaCardStatusEnum.OBTAINED_QUOTA_SUCCESSFULLY
        ) {
          res = HomeCardEnum.LOAN_AMOUNT
        }
      }

      if (
        loanCards.value?.adaFlx &&
        loanCards.value?.adaFlx.loanCard &&
        loanCards.value?.adaFlx.loanCard?.loanList &&
        loanCards.value?.adaFlx.loanCard.loanList.length > 0
      ) {
        const latestLoanCard = loanCards.value?.adaFlx.loanCard.loanList[0]
        if (latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.PROCESSING && latestLoanCard.needSign) {
          return HomeCardEnum.WAITING_FOR_SIGNATURE
        }

        if (
          latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.AUDIT ||
          latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.PROCESSING ||
          latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.WITHDRAWING
        ) {
          return HomeCardEnum.PENDING_REPAYMENT_TODAY
        }

        if (latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.UNREPAY) {
          return HomeCardEnum.INSUFFICIENT_AMOUNT
        }

        if (
          latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.ALL_CANCEL ||
          latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.REJECT ||
          latestLoanCard.lifeCycleCode === LiveCycleCodeEnum.FINISH_4WITHDRAW_FAIL
        ) {
          return HomeCardEnum.LOAN_FAILURE
        }
      }

      return res
    })
    const amount = computed(() => {
      if (dispAdaFlxFakeCard.value) {
        return 50000000
      }

      let res = 50000000
      console.log('cardType.value', cardType.value)
      switch (cardType.value) {
        case HomeCardEnum.BEFORE_CREDIT:
          res = 50000000
          break
        case HomeCardEnum.CREDIT_LIMIT_EXPIRED:
        case HomeCardEnum.OBTAINING_QUOTA:
        case HomeCardEnum.INSUFFICIENT_AMOUNT:
          if (loanCards.value?.adaFlx?.quotaCard?.totalAmount > -1) {
            res = loanCards.value?.adaFlx?.quotaCard?.totalAmount
          } else {
            res = 50000000
          }
          break
        case HomeCardEnum.LOAN_AMOUNT:
          if (loanCards.value?.adaFlx?.quotaCard?.availableAmount > -1) {
            res = loanCards.value?.adaFlx?.quotaCard?.availableAmount
          } else {
            res = 50000000
          }
          break
        case HomeCardEnum.WAITING_FOR_SIGNATURE:
        case HomeCardEnum.PENDING_REPAYMENT_TODAY:
          if (loanCards.value?.adaFlx?.loanCard?.loanList[0]?.applyAmount > -1) {
            res = loanCards.value?.adaFlx?.loanCard?.loanList[0]?.applyAmount
          } else {
            res = 50000000
          }
          break
        default:
          res = parseInt(maxLoanAmount.value || '50000000')
      }

      return res
    })

    let scrollTop = 0
    const scrollToTop = () => {
      const top = pageRef.value.scrollTop
      scrollTop = top
      const statusBarHeight = parseInt(window.xmpStatusBarHeight)
      if (top > statusBarHeight) {
        isStickstatusBarAndHeader.value = true
      } else {
        isStickstatusBarAndHeader.value = false
      }
    }

    const openUnequalUserAuthPopup = () => {
      createDialog({
        component: UnequalUserAuthPopup,
        parent: this,
        className: '',
        class: '',
        style: '',
        root: ''
      })
    }

    const openProvideLoanAgainPopup = async () => {
      if (protectPopupLock) {
        return
      }

      protectPopupLock = true
      try {
        const response = await popUpShowFlag()
        if (response.result === 0 && response.content?.showFlag) {
          createDialog({
            component: ProvideLoanAgainPopup,
            parent: this,
            className: '',
            class: '',
            style: '',
            root: '',
            screenName: route.meta?.screenName,
            phone: store.getMobile
          }).onOk(async () => {
            const list: fetchP2pHomeBannerItem[] = await fetchP2pList()
            const p2p = list[0]
            trackSAClick({
              tgt_event_id: 'cashcerdas_loanstore_box_click',
              tgt_name: 'cashcerdas-贷超弹窗申请',
              page: route.meta?.screenName,
              params: [store.getMobile, '1', p2p?.name],
              extraParams: {}
            })

            await goToDetailPage(p2p, '弹窗')
          })

          await popUpComplete({ popupId: response.content.popupId })
        }
      } catch (error) {
        console.log('openProvideLoanAgainPopup error:', error)
      } finally {
        protectPopupLock = false
      }
    }

    const getPagesHomeInfo = async () => {
      const res = await fetchPagesHomeInfo({
        sortByString: 'loan_user_count',
        orderString: 'desc'
      })
      if (res.result !== 0) {
        InToast.info(res.resultMessage)
        return
      }

      dispAdaFlxFakeCard.value = res.content?.dispAdaFlxFakeCard as boolean
      maxLoanAmount.value = res.content?.maxLoanAmount as string
      loanCards.value = res.content?.loanCards as ILoanCards
      isFirstReject.value = res.content?.isFirstReject as boolean
    }

    const getP2pList = async () => {
      p2pList.value = await fetchP2pList()
    }

    const getP2pHomeBannerList = async () => {
      loanChanelList.value = await fetchP2pHomeBannerList()
    }

    const getHomeInfo = async () => {
      getPagesHomeInfo()
      getP2pList()
      getP2pHomeBannerList()

      await nextTick()
    }

    const goToDetailPage = async (item: any, source: string) => {
      const deviceInfo = await bridge.getDeviceInfo()
      const gaid = deviceInfo.gaid === 'web not support' ? '' : deviceInfo.gaid

      bridge.link({
        name: 'ChannelDetail',
        query: {
          channelId: item.id,
          channelName: item.name,
          gaid: gaid,
          cardType: cardType.value + '',
          source: source
        }
      })
    }

    const borrowerHandler = async (goRepay: boolean) => {
      if (store.isLogin) {
        if (store.mobile === '087776660523') {
          goToDetailPage(p2pList.value[p2pList.value?.length - 1], '列表')
          return
        }

        const res = await fetchAdaFlxEntrance()
        if (res.result !== 0) {
          InToast.info(res.resultMessage)
          return
        }
        console.log('goRepay:', goRepay)
        if (goRepay) {
          bridge.link(
            (res.content?.url +
              `&target=RepayCodeList&listingId=${loanCards.value.adaFlx.repaymentCard?.loanList[0].listId}&debtId=${loanCards.value.adaFlx.repaymentCard?.loanList[0].debtId}&serialNo=${loanCards.value.adaFlx.repaymentCard?.loanList[0].serialNo}`) as string
          )
          return
        }

        trackSAClick({
          tgt_event_id: 'cashcerdas_loan_click',
          tgt_name: 'cashcerdas-借款卡片-点击',
          page: route.meta?.screenName,
          params: [store.getMobile, getHomeCardEnumText(cardType.value), amount.value + ''],
          extraParams: {}
        })

        if (loanCards.value?.adaFlx?.loanCard?.loanList.length > 0) {
          bridge.link(
            (res.content?.url +
              `&target=LoanDetail&serialNo=${loanCards.value.adaFlx.loanCard?.loanList[0].serialNo}`) as string
          )
          return
        }
        bridge.link(res.content?.url as string)
      } else {
        trackSAClick({
          tgt_event_id: 'cashcerdas_loan_click',
          tgt_name: 'cashcerdas-借款卡片-点击',
          page: route.meta?.screenName,
          params: ['', '未登录'],
          extraParams: {}
        })
        bridge.link({ name: 'Login' })
      }
    }

    const getHomeCard = (firstRejFlag: boolean) => {
      if (firstRejFlag) {
        return [
          <LoanChannelCardList
            v-on:goToDetailPage={(item: any) => goToDetailPage(item, 'banner')}
            loanChanelList={loanChanelList.value}
          ></LoanChannelCardList>,
          <NewHomeCard
            adaFlx={loanCards.value?.adaFlx}
            cardType={cardType.value}
            amount={amount.value}
            v-on:borrowerHandler={borrowerHandler}
          ></NewHomeCard>
        ]
      } else if (firstRejFlag === false) {
        return [
          <HomeCard
            adaFlx={loanCards.value?.adaFlx}
            cardType={cardType.value}
            amount={amount.value}
            v-on:borrowerHandler={borrowerHandler}
          ></HomeCard>,
          <RepaymentCard
            style={{ marginTop: '12px' }}
            adaFlx={loanCards.value?.adaFlx}
            screenName={route.meta?.screenName}
            v-on:borrowerHandler={() => {
              borrowerHandler(true)
            }}
          ></RepaymentCard>,
          isShowGuideCard.value && <GuideCard></GuideCard>
        ]
      } else {
        return null
      }
    }

    onMounted(async () => {
      pageRef.value.addEventListener('scroll', scrollToTop)
      if (store.isLogin) {
        // update firebase token
        const bridge = useBridge()
        bridge.getFirebasePushToken().then((res) => {
          if (res && res.token) {
            savePushToken(res.token, res.notificationOn)
          }
        })
        bridge.requestPermission('notification')
      }
    })

    onBeforeUnmount(() => {
      pageRef.value.removeEventListener('scroll', scrollToTop)
    })

    onActivated(async () => {
      pageRef.value.scrollTo({
        top: scrollTop
      })

      getHomeInfo()

      if (store.isLogin) {
        openProvideLoanAgainPopup()
      }
    })

    bridge.onShow(async () => {
      store.$hydrate()

      getHomeInfo()

      if (store.isLogin) {
        openProvideLoanAgainPopup()
      }
    })

    bridge.dismissBackButton()

    return () => {
      return (
        <div class={css.page} ref={pageRef}>
          <div class={[css.header, isStickstatusBarAndHeader.value ? css.sticky : '']}>
            <p class={css.statusBar}></p>
            <div class={css.headerContent}>
              <p class={css.logo}>CashCerdas</p>
              {/* <img class={css.chat} src={chatUrl} alt="chat"></img> */}
            </div>
          </div>
          <div class={css.main}>
            {getHomeCard(isFirstReject.value)}
            <p class={css.tip}>Tingkat lolos cepat dan cair dalam 3 menit</p>
            <P2PList
              list={p2pList.value}
              v-on:goToDetailPage={(item: any) => goToDetailPage(item, '列表')}
              screenName={route.meta?.screenName}
              phone={store.getMobile}
            ></P2PList>
          </div>
        </div>
      )
    }
  }
})
