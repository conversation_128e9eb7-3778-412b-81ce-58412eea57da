import { defineComponent, computed, ref, PropType, onActivated, onDeactivated, h, onBeforeUnmount } from 'vue'
import { formatAmountCurrencyWithoutUnit } from '@/common/utils/format'
import { IAdaFlx } from '@/module/default/api/response'

import { HomeCardEnum } from '@/module/default/constant/homeConstant'

import css from './HomeCard.module.scss'
import rightClickUrl from '../assets/right_click.png'
import arrowDownRightUrl from '../assets/arrow_down_right.png'
import tenorUrl from '../assets/tenor.png'
import shieldUrl from '../assets/shield.png'
import starUrl from '../assets/star.png'

const StarList = () => {
  return (
    <div class={css.starList}>
      <span>5.0</span>
      <img src={starUrl} alt="star" />
      <img src={starUrl} alt="star" />
      <img src={starUrl} alt="star" />
      <img src={starUrl} alt="star" />
      <img src={starUrl} alt="star" />
    </div>
  )
}

const AdvantageList = () => {
  return (
    <div class={css.advantageList}>
      <div class={css.advantageItem}>
        <img src={tenorUrl} alt="tenor" />
        <p>Tenor 180 Hari</p>
      </div>
      <div class={css.advantageItem}>
        <img src={arrowDownRightUrl} alt="low interest rates" />
        <p>Bunga 0,03%/Hari</p>
      </div>
      <div class={css.advantageItem}>
        <img src={shieldUrl} alt="safe" />
        <p>Berizin OJK</p>
      </div>
    </div>
  )
}

const carouselTextList = [
  'Layanan pinjaman disediakan oleh AdaKami!',
  'Biaya transparan, dipercaya oleh <span>34 jutaan</span> pengguna!',
  'Tingkat lolos hingga 97.8%, dana cair cepat!'
]

export default defineComponent({
  name: 'HomeCard',
  props: {
    adaFlx: {
      type: Object as PropType<IAdaFlx>,
      default: () => ({})
    },
    cardType: {
      type: Number as PropType<HomeCardEnum>,
      default: HomeCardEnum.LOAN_AMOUNT
    },
    amount: {
      type: Number,
      default: 50000000
    }
  },
  emits: ['borrowerHandler'],
  setup(props, { emit }) {
    let timer: NodeJS.Timeout | null = null

    const carouselTextIndex = ref(0)

    const isBeforeCredit = computed(() => {
      return props.cardType === HomeCardEnum.BEFORE_CREDIT
    })
    const isLoanAmount = computed(() => {
      return props.cardType === HomeCardEnum.LOAN_AMOUNT
    })
    const isShowTip = computed(() => {
      return [
        HomeCardEnum.WAITING_FOR_SIGNATURE,
        HomeCardEnum.LOAN_FAILURE,
        HomeCardEnum.PENDING_REPAYMENT_TODAY
      ].includes(props.cardType)
    })
    const buttonText = computed(() => {
      switch (props.cardType) {
        case HomeCardEnum.BEFORE_CREDIT:
          return 'Pinjam Sekarang'
        case HomeCardEnum.PENDING_REPAYMENT_TODAY:
          return 'Lihat Detail'
        case HomeCardEnum.WAITING_FOR_SIGNATURE:
          return 'Klik untuk tanda tangan'
        case HomeCardEnum.LOAN_FAILURE:
          return 'View Details'
        case HomeCardEnum.LOAN_AMOUNT:
          return 'Pinjam Sekarang'
        case HomeCardEnum.INSUFFICIENT_AMOUNT:
        case HomeCardEnum.CREDIT_LIMIT_EXPIRED:
        case HomeCardEnum.OBTAINING_QUOTA:
          return 'Cek status terbaru'
        default:
          return ''
      }
    })
    const buttonMarginBottom = computed(() => {
      switch (props.cardType) {
        case HomeCardEnum.BEFORE_CREDIT:
        case HomeCardEnum.PENDING_REPAYMENT_TODAY:
        case HomeCardEnum.WAITING_FOR_SIGNATURE:
        case HomeCardEnum.LOAN_FAILURE:
          return '20px'
        case HomeCardEnum.LOAN_AMOUNT:
        case HomeCardEnum.CREDIT_LIMIT_EXPIRED:
        case HomeCardEnum.OBTAINING_QUOTA:
        case HomeCardEnum.INSUFFICIENT_AMOUNT:
          return '30px'
        default:
          return ''
      }
    })

    const buttonMarginTop = computed(() => {
      switch (props.cardType) {
        case HomeCardEnum.BEFORE_CREDIT:
          return '23px'
        case HomeCardEnum.PENDING_REPAYMENT_TODAY:
        case HomeCardEnum.WAITING_FOR_SIGNATURE:
        case HomeCardEnum.LOAN_FAILURE:
          return '25px'
        case HomeCardEnum.LOAN_AMOUNT:
        case HomeCardEnum.CREDIT_LIMIT_EXPIRED:
        case HomeCardEnum.OBTAINING_QUOTA:
        case HomeCardEnum.INSUFFICIENT_AMOUNT:
          return '16px'
        default:
          return ''
      }
    })

    const isShowMaximumLimit = computed(() => {
      return ![
        HomeCardEnum.CREDIT_LIMIT_EXPIRED,
        HomeCardEnum.OBTAINING_QUOTA,
        HomeCardEnum.INSUFFICIENT_AMOUNT
      ].includes(props.cardType)
    })
    const isShowAmount = computed(() => {
      return ![
        HomeCardEnum.CREDIT_LIMIT_EXPIRED,
        HomeCardEnum.OBTAINING_QUOTA,
        HomeCardEnum.INSUFFICIENT_AMOUNT
      ].includes(props.cardType)
    })
    const isShowAmountDesc = computed(() => {
      return (
        ![
          HomeCardEnum.CREDIT_LIMIT_EXPIRED,
          HomeCardEnum.OBTAINING_QUOTA,
          HomeCardEnum.INSUFFICIENT_AMOUNT
        ].includes(props.cardType) && !isBeforeCredit.value
      )
    })
    const isShowReason = computed(() => {
      return [
        HomeCardEnum.CREDIT_LIMIT_EXPIRED,
        HomeCardEnum.OBTAINING_QUOTA,
        HomeCardEnum.INSUFFICIENT_AMOUNT
      ].includes(props.cardType)
    })
    const isShowStarList = computed(() => {
      return props.cardType === HomeCardEnum.BEFORE_CREDIT
    })
    const reason = computed(() => {
      if (isShowReason.value) {
        switch (props.cardType) {
          case HomeCardEnum.INSUFFICIENT_AMOUNT:
            return 'Maaf, untuk semetara kami tidak dapat menyediakan layanan cicilan untuk Anda.'
          case HomeCardEnum.CREDIT_LIMIT_EXPIRED:
            return 'Maaf, limit Anda telah kedaluwarsa. Silakan klik tombol untuk mengecek kembali.'
          case HomeCardEnum.OBTAINING_QUOTA:
            return 'Informasi Anda telah diterima dan limit pinjaman Anda sedang diproses. Proses review akan memakan waktu sekitar 60 detik, harap bersabar. Terima kasih.'
        }
      }

      return 'Maaf, untuk semetara kami tidak dapat menyediakan layanan cicilan untuk Anda.'
    })
    const maximumLimitText = computed(() => {
      switch (props.cardType) {
        case HomeCardEnum.BEFORE_CREDIT:
          return 'Limit Maksimal(Rp)'
        default:
          if (props?.adaFlx?.quotaCard) {
            if (props?.adaFlx?.loanCard?.loanList?.length > 0) {
              return 'Jumlah pinjaman(Rp)'
            } else {
              return 'Sisa Limit Pinjaman (Rp)'
            }
          }
      }

      return 'Limit Maksimal(Rp)'
    })

    const borrowerHandler = async () => {
      emit('borrowerHandler')
    }

    onActivated(async () => {
      timer = setInterval(() => {
        carouselTextIndex.value = (carouselTextIndex.value + 1) % carouselTextList.length
      }, 10000)
    })

    onDeactivated(() => {
      clearInterval(timer as NodeJS.Timeout)
    })

    onBeforeUnmount(() => {
      clearInterval(timer as NodeJS.Timeout)
    })

    return () => {
      return (
        <div
          class={[css.card, isBeforeCredit.value || isLoanAmount.value ? css.cardBg : '']}
          onClick={borrowerHandler}
        >
          {isBeforeCredit.value && (
            <div class={css.marquee}>
              <span
                class={css.carousel}
                id="carousel"
                domPropsInnerHTML={carouselTextList[carouselTextIndex.value]}
              ></span>
            </div>
          )}

          {isShowStarList.value && <StarList />}

          {isShowTip.value && (
            <div class={css.tip}>{props?.adaFlx?.loanCard?.loanList[0]?.lifeCycleCodeTitle || ''}</div>
          )}

          {isShowMaximumLimit.value && (
            <p
              class={[
                css.maximumLimit,
                isBeforeCredit.value ? css.maximumLimitSpecial : css.maximumLimitNormal
              ]}
            >
              {maximumLimitText.value}
            </p>
          )}

          {isShowAmount.value && (
            <p class={[css.amount, isBeforeCredit.value ? css.amountSpecial : css.amountNormal]}>
              {formatAmountCurrencyWithoutUnit(props.amount)}
            </p>
          )}

          {isShowAmountDesc.value && <p class={css.amountDesc}>Bunga Mulai 0.03% | Cair dalam 24 Jam</p>}

          {isShowReason.value && <p class={css.reason}>{reason.value}</p>}

          <button
            style={{
              marginTop: buttonMarginTop.value,
              marginBottom: buttonMarginBottom.value
            }}
            class={[css.goToNext, css.scaleAni]}
          >
            <span>{buttonText.value}</span>
            {(isBeforeCredit.value || isLoanAmount.value) && <img src={rightClickUrl} alt="right click" />}

            {(isBeforeCredit.value || isLoanAmount.value) && <p class={css.goToNextDesc}>Cepat Cair</p>}
          </button>

          {isBeforeCredit.value && <AdvantageList></AdvantageList>}
        </div>
      )
    }
  }
})
