<template>
  <swiper v-if="props.loanChanelList.length > 0" ref="mySwiper" class="swiper" :options="swiperOption">
    <swiper-slide v-for="item in props.loanChanelList" :key="item.id">
      <div class="card" @click="() => clickHandler(item)">
        <div class="top">
          <div class="iconBar">
            <img class="icon" :src="item.icon" />
            <span class="title">{{ item.name }}</span>
          </div>
        </div>
        <div class="labelAmount">Limit Maks.(Rp)</div>
        <div class="valueAmount">{{ formatAmountCurrencyWithoutUnit(item.maxAmount) }}</div>
        <div class="rateLine">
          <span class="text">Cepat Lolos</span>
          <span class="divider">|</span>
          <span class="text"><PERSON><PERSON><PERSON></span>
          <span class="bold">{{ formatNumber(item.minDayInterestRate) }}%</span>
        </div>

        <div :class="['btn', 'scaleAni']">
          Ajukan
          <div class="flowTip">Cair dalam 3 menit</div>
        </div>
      </div>
    </swiper-slide>
    <template #pagination>
      <div class="swiper-pagination"></div>
    </template>
  </swiper>
</template>

<script setup>
import { reactive, ref, onMounted, defineEmits, onBeforeUnmount, defineProps } from 'vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import { formatAmountCurrencyWithoutUnit, formatNumber } from '@/common/utils/format'
import { trackSAImp, trackSAClick } from '@/common/track/trackSA'
import { useRoute } from 'vue-router/composables'

import 'swiper/css/swiper.min.css'
import useUserInfoStore from '@/module/default/store/modules/userInfo'

const props = defineProps({
  loanChanelList: {
    type: Array,
    required: true
  }
})
const emit = defineEmits(['goToDetailPage'])
const timer = ref(null)
const store = useUserInfoStore()
const route = useRoute()
const swiperOption = reactive({
  slidesPerView: 'auto',
  spaceBetween: 30,
  preventLinksPropagation: false,
  pagination: {
    el: '.swiper-pagination',
    bulletClass: 'my-bullet',
    bulletActiveClass: 'my-bullet-active',
    clickable: true
  }
})
const mySwiper = ref(null)
const currentIndex = ref(-1)

const clickHandler = (item) => {
  trackSAClick({
    tgt_event_id: 'cashcerdas_loanstore_card_click',
    tgt_name: 'cashcerdas-贷超卡片',
    page: route.meta?.screenName,
    params: [store.getMobile, '1', item?.name],
    extraParams: {}
  })

  emit('goToDetailPage', item)
}

onMounted(async () => {
  timer.value = setInterval(() => {
    const swiperInstance = mySwiper.value.$swiper
    if (currentIndex.value !== swiperInstance.snapIndex) {
      trackSAImp({
        tgt_event_id: 'cashcerdas_loanstore_card_imp',
        tgt_name: 'cashcerdas-贷超卡片',
        page: route.meta?.screenName,
        params: [store.getMobile, '1', props.loanChanelList[swiperInstance.snapIndex]?.name],
        extraParams: {}
      })

      currentIndex.value = swiperInstance.snapIndex
    }
  }, 500)
})

onBeforeUnmount(async () => {
  clearInterval(timer.value)
})
</script>

<style lang="scss" scoped>
.swiper {
  height: 205px;
  margin-bottom: 16px;
}

.swiper-pagination {
  bottom: 0px;
  z-index: 9999;
}

.swiper-slide {
  width: 273px;
  height: 205px;
}

.card {
  box-sizing: border-box;
  padding: 16px;
  background-color: white;
  box-shadow: 0 1px 5px 0 rgba(220, 220, 220, 0.25);
  border-radius: 10px;
  height: 192px;
  width: 273px;

  .top {
    display: flex;
    flex-direction: row;

    .iconBar {
      padding: 2px;
      display: flex;
      flex-direction: row;
      align-items: center;
      border-radius: 6px;
      box-sizing: border-box;
      border: 0.5px solid #e1e1e1;

      .icon {
        margin: 2px;
        width: 16px;
        height: 16px;
        display: inline-block;
      }

      .title {
        margin: 2px;
        line-height: 16px;
        font-size: 11px;
        color: #4d4d4d;
      }
    }
  }

  .labelAmount {
    margin-top: 3px;
    font-size: 11px;
    line-height: 16px;
    color: #646464;
  }

  .valueAmount {
    font-size: 32px;
    font-weight: 600;
    line-height: 36px;
    color: #000000;
    font-family: Commissioner-Regular, serif;
  }

  .rateLine {
    margin-top: 4px;
    font-size: 10px;
    display: flex;
    align-items: center;

    .text {
      display: inline-block;
      font-size: 10px;
      line-height: 14px;
      color: #646464;
    }

    .divider {
      display: inline-block;
      margin: auto 4px;
      color: #e0e0e0;
      font-size: 7px;
    }

    .bold {
      margin-left: 4px;
      font-size: 10px;
      font-weight: 600;
      line-height: 14px;
      color: #3f3f3f;
    }
  }

  .btn {
    margin-top: 20px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    font-weight: bold;
    color: white;
    background-color: #068bfd;
    border-radius: 8px;
    box-shadow: 0 1px 8px 0 rgba(0, 2, 5, 0.1);
    position: relative;

    .flowTip {
      position: absolute;
      right: -4px;
      top: -9px;
      z-index: 999;

      height: 16px;
      font-size: 10px;
      font-weight: 600;
      line-height: 14px;
      color: #ffffff;
      padding: 1px 6px;

      border-radius: 6px 10px 10px 1px;
      background: linear-gradient(90deg, #ff8927 8%, #ff6400 99%), #d8d8d8;
    }
  }

  .scaleAni {
    animation-name: scaleAnimation; // 动画名
    animation-duration: 2s; // 动画时长
    animation-iteration-count: infinite; // 永久动画
    transition-timing-function: ease-in-out; // 动画过渡
  }

  @keyframes scaleAnimation {
    // 动画设置
    0% {
      transform: scale(1);
    }

    25% {
      transform: scale(1.05);
    }

    50% {
      transform: scale(1);
    }

    75% {
      transform: scale(1.05);
    }
  }
}
</style>

<style lang="scss">
.my-bullet {
  width: 9px;
  height: 3px;
  display: inline-block;
  border-radius: 5px;
  background: #d3d8dc;
  margin-right: 4px;
}
.my-bullet-active {
  background: #068bfd;
}
</style>
