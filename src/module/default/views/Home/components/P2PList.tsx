import { defineComponent } from 'vue'
import { formatAmountCurrencyWithoutUnit, formatNumber } from '@/common/utils/format'
import css from './P2PList.module.scss'
import { trackSAClick } from '@/common/track/trackSA'
import useUserInfoStore from '@/module/default/store/modules/userInfo'

export default defineComponent({
  props: {
    list: {
      type: Array,
      default: () => []
    },
    screenName: {
      type: String,
      default: ''
    },
    phone: {
      type: String,
      default: ''
    }
  },

  emits: ['goToDetailPage'],
  setup(props, { emit }) {
    const store = useUserInfoStore()
    const goToDetailPage = (item: any) => {
      trackSAClick({
        tgt_event_id: 'cashcerdas_loanstore_list_click',
        tgt_name: 'cashcerdas-贷超列表',
        page: props.screenName,
        params: [props.phone, '1', item?.name],
        extraParams: {}
      })
      emit('goToDetailPage', item)
    }
    return () => {
      return (
        <div class={css.list}>
          {props.list.map((item: any) => {
            return (
              <div class={css.item} onClick={() => goToDetailPage(item)}>
                <div class={css.itemHeader}>
                  <img src={item.icon} alt="360" />
                  <p class={css.itemTitle}>{item.name}</p>
                  <div class={css.tagList}>
                    {item.tags.map((tag: any) => {
                      return <p>{tag}</p>
                    })}
                  </div>
                </div>
                <div class={css.itemMain}>
                  <div class={css.itemMainLeft}>
                    <p class={css.amountLimitDesc}>Limit Maks.(Rp)</p>
                    <p class={css.amountLimit}>{formatAmountCurrencyWithoutUnit(item.maxAmount)}</p>
                  </div>
                  <div class={css.itemMainMiddle}>
                    <p class={css.dailyInterestDesc}>Daily interest</p>
                    <p class={css.dailyInterest}>{formatNumber(item.maxDayInterestRate)}%</p>
                  </div>
                  <div class={css.itemMainRight}>
                    <button>Ajukan</button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )
    }
  }
})
