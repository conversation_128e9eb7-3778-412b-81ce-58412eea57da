import { InDialog } from '@/common/components/InDialog'
import { defineComponent, ref, onMounted, computed, onUnmounted } from 'vue'

import css from './ProvideLoanAgainPopup.module.scss'

import provideLoanImageUrl from '../assets/provideLoan.png'
import btnImageUrl from '../assets/btn.png'
import closeImageUrl from '../assets/close.png'
import { trackSAClick, trackSAImp } from '@/common/track/trackSA'

export default defineComponent({
  name: 'ProvideLoanAgainPopup',
  props: {
    screenName: {
      type: String,
      default: ''
    },
    phone: {
      type: String,
      default: ''
    }
  },
  emits: ['hide', 'ok'],
  setup(props, { expose, emit }) {
    const dialogRef = ref<any>(null)
    const remainingTime = ref(12 * 60 * 60)
    const timer = ref<NodeJS.Timer>()
    const popupId = ref<number>(0)

    const hours = computed(() => {
      return Math.floor(remainingTime.value / 3600)
        .toString()
        .padStart(2, '0')
    })

    const minutes = computed(() => {
      return Math.floor((remainingTime.value % 3600) / 60)
        .toString()
        .padStart(2, '0')
    })

    const seconds = computed(() => {
      return Math.floor(remainingTime.value % 60)
        .toString()
        .padStart(2, '0')
    })

    const show = () => {
      dialogRef.value.show()
    }

    const hide = () => {
      dialogRef.value.hide()
    }

    const handleHide = () => {
      dialogRef.value.visible = false
      setTimeout(() => {
        emit('hide')
      }, 300)

      trackSAClick({
        tgt_event_id: 'cashcerdas_loanstore_box_close',
        tgt_name: 'cashcerdas-贷超弹窗关闭',
        page: props.screenName,
        params: [props.phone, '1'],
        extraParams: {}
      })
    }

    const nextHandler = () => {
      emit('ok')

      dialogRef.value.visible = false
      setTimeout(() => {
        emit('hide')
      }, 300)
    }

    onMounted(async () => {
      timer.value = setInterval(() => {
        remainingTime.value--

        if (remainingTime.value <= 0) {
          handleHide()
        }
      }, 1000)

      trackSAImp({
        tgt_event_id: 'cashcerdas_loanstore_box_imp',
        tgt_name: 'cashcerdas-贷超弹窗',
        page: props.screenName,
        params: [props.phone, '1'],
        extraParams: {}
      })
    })

    onUnmounted(() => {
      clearInterval(timer.value)
    })

    expose({
      show,
      hide
    })

    return () => (
      <InDialog ref={dialogRef} on-hide={handleHide}>
        <div class={css.popup}>
          <div class={css.card}>
            <img class={css.provideLoan} src={provideLoanImageUrl} alt="provide loan" />
            <div class={css.btn} onClick={nextHandler}>
              <img src={btnImageUrl} alt="btn"></img>
              <p>Saya Mau Coba</p>
            </div>

            <div class={css.countdown}>
              <p>{hours.value}</p>
              <span>:</span>
              <p>{minutes.value}</p>
              <span>:</span>
              <p>{seconds.value}</p>
            </div>
          </div>
          <img class={css.close} src={closeImageUrl} alt="" onClick={handleHide} />
        </div>
      </InDialog>
    )
  }
})
