import { defineComponent, PropType, computed } from 'vue'
import { IAdaFlx, IRepaymentCardLoan } from '@/module/default/api/response'
import { HomeCardEnum, RepaymentCardEnum } from '@/module/default/constant/homeConstant'

import css from './NewHomeCard.module.scss'

const ONE_DAY = 60 * 60 * 24 * 1000

export default defineComponent({
  props: {
    adaFlx: {
      type: Object as PropType<IAdaFlx>,
      default: () => ({})
    },
    cardType: {
      type: Number as PropType<HomeCardEnum>,
      default: HomeCardEnum.LOAN_AMOUNT
    },
    amount: {
      type: Number,
      default: 50000000
    }
  },
  emits: ['borrowerHandler'],

  setup(props, { emit }) {
    const now = new Date().getTime()
    const isShowCard = computed(() => {
      return (
        props?.adaFlx &&
        props?.adaFlx.repaymentCard &&
        props?.adaFlx.repaymentCard.loanList &&
        props?.adaFlx.repaymentCard.loanList.length > 0
      )
    })

    const latestRepaymentCard = computed(() => {
      if (
        isShowCard.value &&
        props?.adaFlx &&
        props?.adaFlx.repaymentCard &&
        props?.adaFlx.repaymentCard.loanList
      ) {
        return props?.adaFlx.repaymentCard.loanList[0] as IRepaymentCardLoan
      }

      return {} as IRepaymentCardLoan
    })

    const type = computed(() => {
      if (latestRepaymentCard.value.dueDate - now < 0) {
        return RepaymentCardEnum.OVERDUE
      } else {
        if (latestRepaymentCard.value.dueDate - now < ONE_DAY * 8) {
          return RepaymentCardEnum.PENDING_REPAYMENT_TODAY
        } else {
          return RepaymentCardEnum.PENDING_REPAYMENT
        }
      }
    })

    const timeLeftText = computed(() => {
      if (props.cardType === HomeCardEnum.BEFORE_CREDIT) {
        return `Tersisa ${Math.floor((latestRepaymentCard.value.dueDate - now) / ONE_DAY)} hari`
      } else if (type.value === RepaymentCardEnum.OVERDUE) {
        return `Lewat ${latestRepaymentCard.value.overdueDay} hari`
      } else {
        return ''
      }
    })

    const isShowTip = computed(() => {
      return type.value === RepaymentCardEnum.OVERDUE
    })

    const borrowerHandler = async () => {
      emit('borrowerHandler')
    }

    return () => {
      return (
        <div class={css.newHomePage} onClick={borrowerHandler}>
          <p class={css.title}>Pengajuan Limit Gagal</p>
          <p class={css.reason}>Maaf, kamu belum bisa dapat limit saat ini.</p>

          {isShowTip.value && <div class={css.tip}>{timeLeftText.value}</div>}

          {isShowTip.value && <button>Bayar Segera</button>}
        </div>
      )
    }
  }
})
