.popup {
    width: 100vw;
    height: 482px;
    position: absolute;
    bottom: 0;
    left: 30px;
    border-radius: 12px 12px 0px 0px;

    .card {
        position: relative;
        width: 300px;
        height: 320px;

        .provideLoan {
            width: 300px;
            height: 320px;
        }
    }

    .close {
        margin-top: 30px;
        margin-left: 132px;
        width: 36px;
        height: 36px;
    }
}


.btn {
    position: absolute;
    bottom: 0;
    left: 8px;

    img {
        width: 284px;
        height: 96px;
    }

    p {
        position: absolute;
        top: 32px;
        left: 83px;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        text-align: center;
        color: #FFFFFF;
    }
}

.countdown {
    display: flex;
    align-items: center;
    position: absolute;
    right: 65px;
    top: 196px;

    p {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        background: #FE7C17;
        font-size: 12px;
        font-weight: bold;
        line-height: 12px;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    span {
        color: #FE7C17;
        display: inline-block;
        margin: 0 4px;

    }
}