.card {
    width: 100%;
    min-height: 156px;
    border-radius: 12px;
    box-sizing: border-box;
    position: relative;
    background-color: #068BFD;
    overflow: hidden;
    box-shadow: 0px 4px 10px 0px rgba(24, 87, 182, 0.22);

    &Bg {
        background-image: url('../assets/home_card_bg.png');
        background-size: cover;
    }
}

.marquee {
    overflow: hidden;
    white-space: nowrap;
    width: 336px;
    box-sizing: border-box;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    padding: 0 16px;

    .carousel {
        display: inline-block;
        font-family: Opensans, serif;
        font-size: 10px;
        line-height: 12px;
        color: #fff;
        height: 32px;
        display: flex;
        align-items: center;
        animation: marqueeStep 10s linear infinite;
        width: 100%;
        white-space: pre-wrap;

        span {
            font-weight: bold;
            font-size: 12px;
        }
    }
}

@keyframes marqueeStep {
    0% {
        transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
    }
}



.tip {
    font-size: 10px;
    font-weight: 600;
    line-height: 12px;
    color: #FFFFFF;
    font-family: Opensans, serif;
    height: 22px;
    border-radius: 0px 12px 0px 12px;
    background: #FFBF0E;
    padding: 4px 10px 6px 10px;
    position: absolute;
    right: 0;
    top: 0;
}


.maximumLimit {
    padding: 0 16px;
    font-family: Opensans, serif;
    font-size: 11px;
    line-height: 16px;
    color: rgba(255, 255, 255, 0.8);

    &Normal {
        margin-top: 20px;
    }

    &Special {
        margin-top: 6px;
    }
}

.amount {
    padding: 0 16px;
    font-family: Commissioner-Regular, serif;
    font-weight: 600;
    line-height: 120%;
    color: #FFFFFF;

    &Normal {
        font-size: 36px;
        height: 44px;
        margin-top: 7px;
    }

    &Special {
        font-size: 40px;
        height: 48px;
        margin-top: 4px;
    }
}

.amountDesc {
    font-family: Opensans, serif;
    font-size: 12px;
    line-height: 16px;
    color: #FFFFFF;
    margin-top: 12px;
    padding: 0 16px;
}

.goToNext {
    margin: 0 16px;
    height: 44px;
    width: 304px;
    border-radius: 8px;
    background: #FFFFFF;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    span {
        font-family: Opensans, serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;
        color: #068BFD;
    }

    img {
        margin-left: 2px;
        width: 20px;
        height: 20px;
    }

    .goToNextDesc {
        position: absolute;
        right: -6px;
        top: -9px;
        border-radius: 6px 10px 10px 1px;
        opacity: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 1px 6px;
        background: linear-gradient(90deg, #FF8927 8%, #FF6400 99%), #D8D8D8;
        font-family: Opensans, serif;
        font-size: 10px;
        font-weight: 600;
        line-height: 14px;
        color: #FFFFFF;
    }
}

.advantageList {
    display: flex;
    justify-content: space-around;
    padding: 0 16px;
    margin-bottom: 32px;
}

.advantageItem {
    width: 98px;
    height: 36px;
    background: rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    padding: 5px 8px;

    display: flex;
    align-items: center;

    img {
        width: 16px;
        height: 16px;
    }


    p {
        font-family: Opensans, serif;
        font-size: 10px;
        line-height: 13px;
        color: #FFFFFF;
        margin-left: 6px;
        margin-right: 17px;

        &:last-child {
            margin-right: 0px;
        }
    }
}


.scaleAni {
    animation-name: scaleAnimation; // 动画名
    animation-duration: 2s; // 动画时长
    animation-iteration-count: infinite; // 永久动画
    transition-timing-function: ease-in-out; // 动画过渡
}

@keyframes scaleAnimation {

    // 动画设置
    0% {
        transform: scale(1);
    }

    25% {
        transform: scale(1.05);
    }

    50% {
        transform: scale(1);
    }

    75% {
        transform: scale(1.05);
    }
}

.reason {
    margin: 30px 16px 0 16px;
    font-family: Opensans, serif;
    font-size: 14px;
    line-height: 20px;
    color: rgba(255, 255, 255, 0.9);
    white-space: wrap;
}


.starList {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-family: Opensans, serif;
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    height: 16px;
    color: #FFE11B;
    margin-top: 16px;
    margin-right: 16px;
    right: 0;

    span {
        margin-right: 3px;

    }

    img {
        width: 10px;
        height: 10px;

    }
}