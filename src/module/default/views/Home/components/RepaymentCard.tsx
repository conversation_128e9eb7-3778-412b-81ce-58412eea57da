import { defineComponent, computed, PropType } from 'vue'
import { formatTimeToDesc, formatAmountCurrencyWithoutUnit } from '@/common/utils/format'
import { getRepaymentCardEnumText, RepaymentCardEnum } from '@/module/default/constant/homeConstant'
import { trackSAClick } from '@/common/track/trackSA'
import useUserInfoStore from '@/module/default/store/modules/userInfo'

import { IAdaFlx, IRepaymentCardLoan } from '@/module/default/api/response'

import css from './RepaymentCard.module.scss'

const ONE_DAY = 60 * 60 * 24 * 1000

export default defineComponent({
  name: 'RepaymentCard',
  props: {
    adaFlx: {
      type: Object as PropType<IAdaFlx>,
      default: () => ({})
    },
    screenName: {
      type: String,
      default: ''
    }
  },
  emits: ['borrowerHandler'],
  setup(props, { emit }) {
    const store = useUserInfoStore()
    const now = new Date().getTime()

    const isShowCard = computed(() => {
      return (
        props?.adaFlx &&
        props?.adaFlx.repaymentCard &&
        props?.adaFlx.repaymentCard.loanList &&
        props?.adaFlx.repaymentCard.loanList.length > 0
      )
    })
    const latestRepaymentCard = computed(() => {
      if (
        isShowCard.value &&
        props?.adaFlx &&
        props?.adaFlx.repaymentCard &&
        props?.adaFlx.repaymentCard.loanList
      ) {
        return props?.adaFlx.repaymentCard.loanList[0] as IRepaymentCardLoan
      }

      return {} as IRepaymentCardLoan
    })
    const cardType = computed(() => {
      if (latestRepaymentCard.value.dueDate - now < 0) {
        return RepaymentCardEnum.OVERDUE
      } else {
        if (latestRepaymentCard.value.dueDate - now < ONE_DAY * 8) {
          return RepaymentCardEnum.PENDING_REPAYMENT_TODAY
        } else {
          return RepaymentCardEnum.PENDING_REPAYMENT
        }
      }
    })
    const bgColor = computed(() => {
      if (
        cardType.value === RepaymentCardEnum.PENDING_REPAYMENT ||
        cardType.value === RepaymentCardEnum.PENDING_REPAYMENT_TODAY
      ) {
        return '#00CACA'
      } else {
        return '#FF883E'
      }
    })
    const isShowTimeLeft = computed(() => {
      if (cardType.value === RepaymentCardEnum.PENDING_REPAYMENT) {
        return false
      } else {
        return true
      }
    })
    const timeLeftText = computed(() => {
      if (cardType.value === RepaymentCardEnum.PENDING_REPAYMENT_TODAY) {
        return `Tersisa ${Math.floor((latestRepaymentCard.value.dueDate - now) / ONE_DAY)} hari`
      } else if (cardType.value === RepaymentCardEnum.OVERDUE) {
        return `Lewat ${latestRepaymentCard.value.overdueDay} hari`
      } else {
        return ''
      }
    })

    const timeLeftColor = computed(() => {
      if (cardType.value === RepaymentCardEnum.PENDING_REPAYMENT_TODAY) {
        return '#3C7F3C'
      } else if (cardType.value === RepaymentCardEnum.OVERDUE) {
        return '#FF5410'
      } else {
        return '#fff'
      }
    })

    const borrowerHandler = async () => {
      trackSAClick({
        tgt_event_id: 'cashcerdas_repay_click',
        tgt_name: 'cashcerdas-还款卡片-点击',
        page: props.screenName,
        params: [store.getMobile, getRepaymentCardEnumText(cardType.value)],
        extraParams: {}
      })

      emit('borrowerHandler')
    }

    return () => {
      if (isShowCard.value) {
        return (
          <div style={{ backgroundColor: bgColor.value }} class={css.card} onClick={borrowerHandler}>
            <div class={css.paymentDate}>
              <p>Tanggal pembayaran </p>
              <p class={css.date}>{formatTimeToDesc(latestRepaymentCard.value.dueDate)}</p>
            </div>
            {isShowTimeLeft.value && (
              <p class={css.timeLeft} style={{ backgroundColor: timeLeftColor.value }}>
                {timeLeftText.value}
              </p>
            )}
            <p class={css.amountDesc}>Harus dibayar(Rp)</p>
            <p class={css.amount}>{formatAmountCurrencyWithoutUnit(latestRepaymentCard.value.owingAmount)}</p>
            <button class={css.goToRepay}>Bayar Segera</button>
          </div>
        )
      } else {
        return null
      }
    }
  }
})
