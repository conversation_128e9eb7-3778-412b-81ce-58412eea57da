import { InPopup } from '@/common/components/InDialog'
import InCheckbox from '@/common/components/InForm/InCheckbox/InCheckbox.vue'
import { defineComponent, ref } from 'vue'
import unequalUserUrl from '../assets/unequal-user.png'

import css from './UnequalUserAuthPopup.module.scss'

export default defineComponent({
  name: 'UnequalUserAuthPopup',
  emits: ['hide', 'ok'],
  setup(props, { expose, emit }) {
    const popupRef = ref<any>(null)
    const agreeToAgreement = ref(false)
    const show = () => {
      popupRef.value.show()
    }

    const hide = () => {
      popupRef.value.hide()
    }

    const handleHide = () => {
      popupRef.value.visible = false
      setTimeout(() => {
        emit('hide')
      }, 300)
    }

    const changeAgreeToAgreementHandler = () => {
      agreeToAgreement.value = !agreeToAgreement.value
    }

    const nextHandler = () => {
      emit('ok')
    }

    expose({
      show,
      hide
    })

    return () => (
      <InPopup ref={popupRef} disableMaskClose={true} on-hide={handleHide}>
        <div class={css.box}>
          <div class={css.header}>
            <img src={unequalUserUrl} alt="unequal user" />
          </div>
          <div class={css.content}>
            <p class={css.title}>Otorisasi Login</p>
            <p class={css.desc}>
              Untuk meningkatkan layanan dan pengalaman pinjaman Anda, TCF meminta izin untuk mengumpulkan
              data dari PDI. Privasi Anda akan selalu menjadi prioritas kami.
            </p>

            <div class={css.protocol}>
              <InCheckbox
                value={agreeToAgreement.value}
                on-input={changeAgreeToAgreementHandler}
                width={14}
                height={14}
                svgWidth={7}
                svgHeight={7}
              ></InCheckbox>
              <p>
                Saya telah baca dan menyetujui <a href="">kententuan</a> dan <a href="">perjanjian privasi</a>
              </p>
            </div>

            <button class={css.next} disabled={!agreeToAgreement.value} onClick={nextHandler}>
              Setuju
            </button>

            <button class={css.cancel} onClick={handleHide}>
              Batal
            </button>
          </div>
        </div>
      </InPopup>
    )
  }
})
