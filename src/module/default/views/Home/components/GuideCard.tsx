import { defineComponent, ref, computed, onMounted } from 'vue'

import css from './GuideCard.module.scss'
import applyLoanUrl from '../assets/applyLoan.png'
import nextUrl from '../assets/next.png'
import infoUrl from '../assets/info.png'
import phoneUrl from '../assets/phone.png'

export default defineComponent({
  name: 'GuideCard',
  setup(props) {
    return () => {
      return (
        <div class={css.card}>
          <p class={css.title}>Pinjaman Cepat Hanya 3 Langkah</p>
          <div class={css.content}>
            <div class={css.item}>
              <img class={css.img} src={phoneUrl} alt="phone" />
              <p>Otorisasi <PERSON>gin</p>
            </div>
            <div class={css.item}>
              <img class={css.next} src={nextUrl} alt="next" />
            </div>
            <div class={css.item}>
              <img class={css.img} src={infoUrl} alt="phone" />
              <p>Isi informasinya</p>
            </div>
            <div class={css.item}>
              <img class={css.next} src={nextUrl} alt="next" />
            </div>
            <div class={css.item}>
              <img class={css.img} src={applyLoanUrl} alt="apply loan" />
              <p>Ajukan Pinjaman</p>
            </div>
          </div>
        </div>
      )
    }
  }
})
