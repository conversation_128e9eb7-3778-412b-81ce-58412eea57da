import { defineComponent, ref } from 'vue'
import { useBridge } from '@/common/bridge'
import Header from '@/module/default/components/Header'

import css from './InputOtp.module.scss'

export default defineComponent({
  name: 'InputOtp',
  setup() {
    const bridge = useBridge()

    const nextHandler = () => {
      bridge.link({ name: 'CancelAccountResult', query: { isSuccess: 'true' } })
      // if (false) {
      //   bridge.link({ name: 'CancelAccountResult', query: { isSuccess: 'false' } })
      // }
    }

    return () => (
      <div class={css.otp}>
        <input placeholder="Nomor Verifikasi" type="tel" maxlength={6} />
        <p>Kirim Ulang</p>
      </div>
    )
  }
})
