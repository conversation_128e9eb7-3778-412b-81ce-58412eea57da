.otp {
    border-radius: 6px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #DDDDDD;
    width: 328px;
    margin: 0 16px 16px 16px;
    height: 48px;

    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 12px;


    input {
        font-family: Opensans, serif;
        font-size: 16px;
        line-height: 18px;
        color: #CCCCCC;
        width: 125px;
    }

    p {
        font-family: Opensans, serif;
        font-size: 12px;
        font-weight: 600;
        line-height: 14px;
        color: #068BFD;
    }
}


.in-field {
    display: flex;

    &__inner {
        position: relative;
        width: 100%;

        .in-field--label & {
            padding-top: 6px;
        }
    }

    &__control {
        position: relative;
        border-radius: 4px;
        padding-left: 14px;
        display: flex;
        flex-wrap: nowrap;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            pointer-events: none;
            border: 1px solid #ddd;
            border-radius: inherit;
            transition: border-color 0.36s cubic-bezier(0.4, 0, 0.2, 1);

            .in-field--error & {
                border-color: #dd3424;
            }

            .in-field--float & {
                border-color: var(--main-color, #00903b);
            }
        }
    }

    &__control-container {
        position: relative;
        height: inherit;
        flex: 10000 1 0%;
    }

    &__native {
        width: 100%;
        min-width: 0;
        outline: 0 !important;
        text-decoration: inherit;
        text-transform: inherit;
        border: none;
        border-radius: 0;
        background: none;
        outline: 0;
        padding: 14px 0 14px 0;
        font-size: 16px;
        height: 48px;
        line-height: 20px;
        color: #424147;
        font-family: OpenSans-SemiBold, OpenSans;
        box-sizing: border-box;

        &::-webkit-input-placeholder {
            font-size: 16px;
            font-family: OpenSans-Regular, OpenSans;
            color: #888;
            line-height: 20px;
            padding-left: 3px;
        }
    }

    &__label {
        @include text-ellipsis();
        position: absolute;
        left: 0px;
        top: 14px;
        color: #c4c4c4;
        max-width: 100%;
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0.00937em;
        text-decoration: inherit;
        text-transform: inherit;
        transform-origin: left top;
        transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.324s cubic-bezier(0.4, 0, 0.2, 1),
            color 0.324s;
        background-color: #fff;
        font-family: OpenSans-Regular, OpenSans;

        .in-field--float & {
            color: var(--main-color, #00903b);
            transform: translateY(-100%) scale(0.75);
            transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.396s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.324s;
        }

        .in-field--error & {
            color: #dd3424;
            transform: translateY(-100%) scale(0.75);
            transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.396s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.324s;
        }

        .in-field--valid & {
            color: #c4c4c4;
            transform: translateY(-100%) scale(0.75);
            transition: transform 0.36s cubic-bezier(0.4, 0, 0.2, 1), right 0.396s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.324s;
        }
    }

    // &__label-error {
    //   color: #dd3424 !important;
    // }
    &__disabled {
        color: #939393;
    }

    &__bottom {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
        min-height: 12px;
        padding: 1px 8px 0 8px;
    }

    &__message {
        font-size: 10px;
        font-family: AlibabaSansViet;
        color: #888;
        line-height: 12px;

        .in-field--error & {
            color: #dd3424;
        }
    }

    &__append {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-left: 14px;
    }

    &__prepend {
        margin-right: 10px;
        position: relative;
    }
}

.icon-clear {
    @include icon();
    background-image: url('./assets/images/icon-clear.png');
    width: 14px;
    height: 14px;
    margin-right: 12px;
}

.icon-pwd-show {
    @include icon();
    background-image: url('./assets/images/icon-pwd-show.png');
    width: 18px;
    height: 18px;
    margin-right: 12px;
}

.icon-pwd-hide {
    @include icon();
    background-image: url('./assets/images/icon-pwd-hide.png');
    width: 18px;
    height: 18px;
    margin-right: 12px;
}