import { defineComponent, ref, computed, onMounted } from 'vue'
import { useBridge } from '@/common/bridge'
import Header from '@/module/default/components/Header'
import InInputOtp from '@/common/components/InForm/InInputOtp/index.vue'
import { formatPhoneNumberMask } from '@/common/utils/format'
import useUserInfo from '@/module/default/store/modules/userInfo'
import { InToast } from '@/common/components/InToast'

import { fetchAccountOtp, fetchCancelAccount } from '@/module/default/api'

import css from './index.module.scss'

export default defineComponent({
  name: 'CancelAccountOtpPage',
  setup() {
    const bridge = useBridge()
    const store = useUserInfo()
    let timer: NodeJS.Timeout | null = null

    const counter = ref(0)
    const otp = ref('')

    const enableSendOtp = computed(() => counter.value <= 0)
    const sendButtonText = computed(() => (enableSendOtp.value ? 'Kirim Ulang' : `${counter.value}s`))

    const inputHandler = (val: string) => {
      otp.value = val
    }

    function startCounter() {
      if (counter.value <= 0) {
        counter.value = 60

        timer = setInterval(() => {
          if (--counter.value <= 0) {
            timer === null || clearInterval(timer)
          }
        }, 1000)
      }
    }

    const sendOtpHandler = async () => {
      if (enableSendOtp.value) {
        const res = await fetchAccountOtp({ mobile: store.mobile, msgType: 100101, verifyType: 1 })
        if (res.result !== 0) {
          InToast.info(res.resultMessage)
          return
        }

        startCounter()
      }
    }

    const submitHandler = async () => {
      const res = await fetchCancelAccount({ mobile: store.mobile, verifyCode: otp.value })
      if (res.result !== 0) {
        InToast.info(res.resultMessage)
        return
      }

      if (res.content?.cancelResult) {
        store.clearInfo()
        bridge.link({ name: 'CancelAccountResult', query: { isSuccess: 'true' } })
      } else {
        bridge.link({ name: 'CancelAccountResult', query: { isSuccess: 'false' } })
      }
    }

    onMounted(() => {
      sendOtpHandler()
    })

    return () => (
      <div class={css.page}>
        <Header title="Hapus Akun"></Header>
        <p class={css.desc}>Nomor HP saat ini {formatPhoneNumberMask(store.mobile, 3, 5)}</p>

        <InInputOtp
          class={css.otp}
          label={'Nomor Verifikasi'}
          clearable={true}
          type="tel"
          maxLength={6}
          value={otp.value}
          onInput={inputHandler}
          sendOtpText={sendButtonText.value}
          disableSendOtp={!enableSendOtp.value}
          v-on:sendOtp={sendOtpHandler}
        ></InInputOtp>
        <button class={css.next} onClick={submitHandler}>
          Selanjutnya
        </button>
      </div>
    )
  }
})
