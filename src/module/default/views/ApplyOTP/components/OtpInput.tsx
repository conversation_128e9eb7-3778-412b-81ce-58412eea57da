import { defineComponent, ref, onMounted, computed } from 'vue'
import css from './OtpInput.module.scss'

enum ICodeInputCategory {
  sms = 'sms',
  ktp = 'ktp',
  mobile = 'mobile',
  bank = 'bank'
}

export default defineComponent({
  name: 'OtpInput',
  props: {
    category: {
      type: String,
      default: 'sms'
    },
    realValue: {
      type: String,
      default: ''
    },
    finishTrigger: {
      type: Function,
      required: true
    }
  },
  emits: ['finishTrigger', 'blur', 'focus'],
  setup(props, { expose, emit }) {
    const currentInputValue = ref('')
    const inputRef = ref()
    const data = {
      focus: false,
      focusTimeStamp: 0
    }

    const needsInputCount = computed(() => {
      if (props.category === 'sms') {
        return 6
      } else if (props.category === 'ktp') {
        return (props.realValue as string).length - 3 - 4
      } else if (props.category === 'mobile') {
        return (props.realValue as string).length - 3 - 4
      } else if (props.category === 'bank') {
        return (props.realValue as string).length - 4 - 4
      }

      return 0
    })

    const resetInputValue = () => {
      currentInputValue.value = ''
    }

    const input = (e: any) => {
      currentInputValue.value = e.target.value
      if (currentInputValue.value && currentInputValue.value.length > needsInputCount.value) {
        currentInputValue.value = currentInputValue.value.slice(0, needsInputCount.value)
      }

      if (currentInputValue.value && currentInputValue.value.length === needsInputCount.value) {
        if (props.finishTrigger) {
          props.finishTrigger(currentInputValue.value, (success: boolean) => {
            if (!success) {
              resetInputValue()
            }
          })
        }
      }
    }

    function onFocus(e: any) {
      data.focus = true
      data.focusTimeStamp = new Date().getTime()
      e.stopPropagation()
    }

    function onBlur() {
      data.focus = false
      const duration = new Date().getTime() - data.focusTimeStamp
      emit('blur', duration)
    }

    return () => {
      return (
        <div class={css.otp_input}>
          <input
            ref={inputRef}
            value={currentInputValue.value}
            type="tel"
            autofocus
            class={css.boxInput}
            onInput={input}
            onFocus={onFocus}
            onBlur={onBlur}
          />

          {Array(needsInputCount.value)
            .fill('')
            .map((digit, index) => (
              <div
                key={index}
                class={[css.digit_box, currentInputValue.value.length - 1 === index ? css.digitBoxFocus : '']}
                onClick={() => inputRef.value.focus()}
              >
                {currentInputValue.value[index]}
              </div>
            ))}
        </div>
      )
    }
  }
})
