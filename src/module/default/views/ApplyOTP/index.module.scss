.page {
    width: 100vw;
    height: 100vh;
    position: relative;
    background-color: #fff;
}

.header {
    width: 100vw;
    padding: 0 16px;
    z-index: 1;
    margin-bottom: 28px;

    .statusBar {
        height: var(--statusBarHeight);
        width: 100%;
    }

    .headerContent {
        width: 100%;
        height: 52px;
    }

    img {
        margin-top: 14px;
        width: 24px;
        height: 24px;
    }
}

.content {
    padding: 0 16px;
    width: 100%;

    .title {
        font-family: Opensans, serif;
        font-size: 20px;
        font-weight: 600;
        line-height: 28px;
        color: #333333;
    }

    .titleDesc {
        margin-top: 8px;
        font-family: Opensans, serif;
        font-size: 12px;
        line-height: 18px;
        color: #646464;
        margin-bottom: 18px;
    }

    .submit {
        margin-top: 26px;
        background-color: #068BFD;
        color: #fff;
        width: 328px;
        height: 46px;
        border-radius: 8px;
        font-family: Opensans, serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
        text-align: center;

        &:disabled {
            background: #D6DFE7;
        }
    }

    .sendBox {
        width: 328px;
        display: flex;
        justify-content: space-between;

        .send {
            font-family: Opensans, serif;
            font-size: 13px;
            margin-top: 8px;
            font-weight: normal;
            letter-spacing: 0;
            color: #068BFD;
            background-color: transparent;
        }

        .send_disabled {
            color: #ADADAD;
        }
    }
}