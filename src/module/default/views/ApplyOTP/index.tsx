import { computed, defineComponent, onMounted, ref } from 'vue'
import backUrl from './assets/back.png'
import OtpInput from './components/OtpInput'
import { useRoute } from 'vue-router/composables'
import { useBridge } from '@/common/bridge'
import { InToast } from '@/common/components/InToast'
import { fetchSmsRegisterOrLogin, fetchAccountOtp } from '@/module/default/api'
import useUserInfo from '@/module/default/store/modules/userInfo'
import { formatPhoneNumberMask } from '@/common/utils/format'
import { trackSAClick, trackSAInput } from '@/common/track/trackSA'

import css from './index.module.scss'

export default defineComponent({
  name: 'ApplyOTP',
  setup() {
    const route = useRoute()
    const bridge = useBridge()
    const store = useUserInfo()
    const otp = ref('')
    const counter = ref(0)
    const otpInputRef = ref()
    let timer: NodeJS.Timeout | null = null

    const enableSendOtp = computed(() => counter.value <= 0)
    const sendButtonText = computed(() => (enableSendOtp.value ? 'Kirim Ulang' : `${counter.value}s`))

    function backHandler() {
      bridge.close()
    }
    function startCounter() {
      if (counter.value <= 0) {
        counter.value = 60

        timer = setInterval(() => {
          if (--counter.value <= 0) {
            timer === null || clearInterval(timer)
          }
        }, 1000)
      }
    }

    const finishTrigger = async (val: string, callback = (result: boolean) => {}) => {
      const res = await fetchSmsRegisterOrLogin({
        mobile: route.query.phoneNumber as string,
        verifyCode: val
      })

      trackSAClick({
        tgt_event_id: 'cashcerdas_vercodelogin_submit',
        tgt_name: 'cashcerdas-验证码登录页-提交按钮',
        page: route.meta?.screenName,
        params: [route.query.phoneNumber as string, res.result + '', res.resultMessage],
        extraParams: {}
      })

      if (res.result !== 0) {
        InToast.info(res.resultMessage)
        callback(res.result === 0)
        return
      }

      const openId = res.content?.openId as string
      const userName = res.content?.userName as string
      const phoneNuber = res.content?.mobile as string
      const token = res.content?.token as string
      store.saveInfo(openId, userName, phoneNuber, token)

      bridge.back(-2)
    }

    const blurOtpHandler = (duration: number) => {
      trackSAInput({
        tgt_event_id: 'cashcerdas_vercode_input_time',
        tgt_name: 'cashcerdas-验证码时间',
        page: route.meta?.screenName,
        params: [route.query.phoneNumber as string, duration + ''],
        extraParams: {}
      })
    }
    const sendOtpHandler = async () => {
      if (enableSendOtp.value) {
        const res = await fetchAccountOtp({
          mobile: route.query.phoneNumber as string,
          msgType: 100001,
          verifyType: 1
        })

        trackSAClick({
          tgt_event_id: 'cashcerdas_register_vercode',
          tgt_name: 'cashcerdas-注册页-验证码获取',
          page: route.meta?.screenName,
          params: [route.query.phoneNumber as string],
          extraParams: {}
        })

        if (res.result !== 0) {
          InToast.info(res.resultMessage)
          return
        }

        startCounter()
      }
    }

    onMounted(() => {
      sendOtpHandler()
    })

    bridge.dismissBackButton()

    return () => {
      return (
        <div class={css.page}>
          <div class={css.header}>
            <p class={css.statusBar}></p>
            <img class={css.chat} src={backUrl} alt="back" onClick={backHandler}></img>
          </div>
          <div class={css.content}>
            <div class={css.title}>
              Kode verifikasi telah dikirim ke{' '}
              {formatPhoneNumberMask(route.query.phoneNumber as string, 3, 4)}
            </div>
            <p class={css.titleDesc}>
              Demi keamanan akun Anda, mohon untuk tidak membagikan kode verifikasi kepada siapapun
            </p>
            <OtpInput on-blur={blurOtpHandler} ref={otpInputRef} finishTrigger={finishTrigger} />
            <div class={css.sendBox}>
              <p></p>
              <button
                class={[css.send, enableSendOtp.value ? '' : css.send_disabled]}
                onClick={sendOtpHandler}
              >
                {sendButtonText.value}
              </button>
            </div>

            <button class={css.submit} disabled={otp.value.length < 6}>
              Kirimkan
            </button>
          </div>
        </div>
      )
    }
  }
})
