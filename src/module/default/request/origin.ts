import axios from 'axios'
import type { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { InToast } from '@/common/components/InToast'
import { InLoading } from '@/common/components/InLoading'
import { sha256 } from 'js-sha256'
import { IRequest, TIMEOUT, HEADER_LN, APP_SECRET, APP_KEY } from '@/module/default/constant/apiConstant'
import { useBridge } from '@/common/bridge'

let deviceInfo = {
  aid: '',
  idfv: '',
  idfa: '',
  gaid: ''
}
async function setDeviceInfo() {
  const bridge = useBridge()
  const info = await bridge.getDeviceInfo()
  deviceInfo = info
}
setDeviceInfo()

function generateRawString(config: InternalAxiosRequestConfig, timestamp: number) {
  const method = config.method as string
  const url = config.url as string
  let rawString = `${method.toUpperCase()}:`

  const urlParts = url.split('?')
  rawString += `${urlParts[0]}?`

  const params = []
  if (urlParts.length >= 2) {
    for (const pair of new URLSearchParams(urlParts[1]).entries()) {
      params.push(pair)
    }
  }
  if (config.params) {
    for (const pair of Object.entries(config.params)) {
      params.push([pair[0], pair[1]?.toString()])
    }
  }
  let isJsonBody = !!config.data
  if (config.data instanceof URLSearchParams) {
    isJsonBody = false
    for (const pair of config.data) {
      params.push(pair)
    }
  } else if (typeof config.data === 'string') {
    isJsonBody = false
    for (const pair of new URLSearchParams(config.data)) {
      params.push(pair)
    }
  }
  params.sort((a, b) => {
    const firstOne = a[0] as string
    const secondOne = b[0] as string
    let result = firstOne.localeCompare(secondOne)
    if (result === 0) {
      const firstTwo = a[1] as string
      const secondTwo = b[1] as string
      result = firstTwo.localeCompare(secondTwo)
    }
    return result
  })
  for (const pair of params) {
    rawString += `${pair[0]}=${pair[1]}&`
  }

  if (isJsonBody) {
    console.log('body:', JSON.stringify(config.data))
    rawString += `body=${sha256(JSON.stringify(config.data))}&`
  }

  rawString += `appSecret=${APP_SECRET}&timestamp=${timestamp}`
  return rawString
}

export class Request {
  axiosInstance: AxiosInstance
  axiosRequest: IRequest
  loadingCount = 0
  //全局Request拦截器
  constructor(request: IRequest) {
    this.axiosRequest = request
    this.axiosInstance = axios.create(request)
    this.axiosInstance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        config.headers['Accept-Language'] = 'in-ID'
        config.headers['X-App-Ver'] = import.meta.env.VITE_APP_VERSION
        config.headers['X-Source'] = '3'
        config.headers['X-App-Channel'] = 'h5'
        config.headers['X-Install-Channel'] = 'h5'
        config.headers['X-AB-TESTING'] = 'A'
        if (deviceInfo.gaid && deviceInfo.gaid !== 'web not support') {
          config.headers['X-Advertising-Id'] = deviceInfo.gaid
        } else {
          config.headers['X-Advertising-Id'] = '00000000-0000-0000-0000-000000000000'
        }

        const timestamp = new Date().getTime()
        const rawString = generateRawString(config, timestamp)
        const signature = sha256(rawString)
        config.headers['X-App-Key'] = APP_KEY
        config.headers['X-Timestamp'] = timestamp
        config.headers['X-Signature'] = signature

        if ((config as IRequest).hideLoading === false) {
          this.showLoading()
        }

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )
    // // //全局Response拦截器
    this.axiosInstance.interceptors.response.use(
      (res: AxiosResponse) => {
        const config = res.config as IRequest
        if (config.hideLoading === false) {
          this.closeLoading()
        }

        return res.data
      },
      (error) => {
        //toast显示
        if (error.config.hideLoading === false) {
          this.closeLoading()
        }

        if (error.config.hideToast === false) {
          console.log(error)
          InToast.info(error.message)
        }

        if (error.config.handleErrorSelf === true) {
          // gateway may return {Result, ResultMessage}
          return {
            result:
              error.response.data && error.response.data.Result
                ? error.response.data.Result
                : error.response.status,
            resultMessage:
              error.response.data && error.response.data.ResultMessage
                ? error.response.data.ResultMessage
                : error.response.statusText
          }
        } else {
          return Promise.reject(error)
        }
      }
    )
  }
  showLoading() {
    this.loadingCount++
    InLoading.show()
  }
  closeLoading() {
    this.loadingCount--
    if (this.loadingCount <= 0) {
      InLoading.close()
      this.loadingCount = 0
    }
  }
  request<T>(request: IRequest): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.axiosInstance
        .request({ ...request, headers: { ...this.axiosRequest.headers, ...request.headers } })
        .then((res) => {
          resolve(res as T)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  post<T>(request: IRequest) {
    const postRequest = Object.assign(request, { method: 'post' })
    return this.request<T>(postRequest)
  }

  get<T>(request: IRequest) {
    const postRequest = Object.assign(request, { method: 'get' })
    return this.request<T>(postRequest)
  }
}

export const requestInstance = new Request({
  baseURL: '/originApi',
  timeout: TIMEOUT,
  headers: {
    'Accept-Language': HEADER_LN
  },
  hideLoading: false,
  hideToast: false
})
