import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { InToast } from '@/common/components/InToast'
import { InLoading } from '@/common/components/InLoading'
import { sha256 } from 'js-sha256'

import {
  IResponse,
  IRequest,
  TIMEOUT,
  HEADER_LN,
  LanguageEnum,
  TCF_MERCHANT,
  TCF_APPID,
  APP_KEY,
  APP_SECRET
} from '@/module/default/constant/apiConstant'
import { useBridge } from '@/common/bridge'

import useUserInfo from '@/module/default/store/modules/userInfo'
import useNativeInfoStore from '@/module/default/store/modules/nativeInfo'
const store = () => {
  return useUserInfo()
}

const nativeInfoStore = () => {
  return useNativeInfoStore()
}

function generateRawString(config: InternalAxiosRequestConfig, timestamp: number) {
  const method = config.method as string
  const url = config.url as string
  let rawString = `${method.toUpperCase()}:`

  const urlParts = url.split('?')
  rawString += `${urlParts[0]}?`

  const params = []
  if (urlParts.length >= 2) {
    for (const pair of new URLSearchParams(urlParts[1]).entries()) {
      params.push(pair)
    }
  }
  if (config.params) {
    for (const pair of Object.entries(config.params)) {
      params.push([pair[0], pair[1]?.toString()])
    }
  }
  let isJsonBody = !!config.data
  if (config.data instanceof URLSearchParams) {
    isJsonBody = false
    for (const pair of config.data) {
      params.push(pair)
    }
  } else if (typeof config.data === 'string') {
    isJsonBody = false
    for (const pair of new URLSearchParams(config.data)) {
      params.push(pair)
    }
  }
  for (const pair of params) {
    rawString += `${pair[0]}=${pair[1]}&`
  }

  if (isJsonBody) {
    console.log('body:', JSON.stringify(config.data))
    rawString += `body=${sha256(JSON.stringify(config.data))}&`
  }

  rawString += `appSecret=${APP_SECRET}&timestamp=${timestamp}`
  return rawString
}

export class Request {
  axiosInstance: AxiosInstance
  axiosRequest: IRequest
  loadingCount = 0
  //全局Request拦截器
  constructor(request: IRequest) {
    this.axiosRequest = request
    this.axiosInstance = axios.create(request)
    this.axiosInstance.interceptors.request.use(
      (res: InternalAxiosRequestConfig) => {
        const timestamp = new Date().getTime()
        const rawString = generateRawString(res, timestamp)
        const signature = sha256(rawString)

        res.headers['X-Timestamp'] = timestamp
        res.headers['X-Signature'] = signature
        res.headers['X-TCF-AID'] = nativeInfoStore().deviceInfo.gaid
        res.headers['X-TCF-ANDROIDID'] = nativeInfoStore().deviceInfo.aid //android device id
        res.headers['X-TCF-TOKEN'] = store().token
        res.headers['X-App-Ver'] = nativeInfoStore().appInfo.version

        res.headers['X-TCF-INSTANCEID'] = nativeInfoStore().appInfo.firebaseInstanceId
        res.headers['X-TCF-OS-VER'] = nativeInfoStore().deviceInfo.osVersion
        res.headers['X-TCF-MODEL'] = nativeInfoStore().deviceInfo.model

        res.headers['X-TCF-APPSFLYERID'] = nativeInfoStore().appsflyerInfo?.afUid
        res.headers['X-TCF-MEDIASOURCE'] = nativeInfoStore().appsflyerInfo?.mediaSource
        res.headers['X-TCF-AGENCY'] = nativeInfoStore().appsflyerInfo?.agency
        res.headers['X-TCF-CAMPAIGN'] = nativeInfoStore().appsflyerInfo?.campaign
        res.headers['X-TCF-ADSET-ID'] = nativeInfoStore().appsflyerInfo?.adsetId

        if ((res as IRequest).hideLoading === false) {
          this.showLoading()
        }
        return res
      },
      (error) => {
        return Promise.reject(error)
      }
    )
    //全局Response拦截器
    this.axiosInstance.interceptors.response.use(
      (res: AxiosResponse) => {
        console.log('api out: ', res)
        const config = res.config as IRequest
        if (config.hideLoading === false) {
          this.closeLoading()
        }
        //toast显示
        if (config.hideToast === false && res.data.result !== 0) {
          if (res.data.result === 3000100) {
            store().clearInfo()
            const bridge = useBridge()
            bridge.link({ name: 'Login' })
          }
          InToast.info(res.data.resultMessage)
        }
        if (res.data.result === 0 || config.handleErrorSelf === true) {
          return res.data
        } else {
          return Promise.reject(res.data)
        }
      },
      (error) => {
        //toast显示
        if (error.config.hideLoading === false) {
          this.closeLoading()
        }

        if (error.response.data.Result === -4000) {
          const bridge = useBridge()
          bridge.link({ name: 'Login' })
          return
        }

        if (error.config.hideToast === false) {
          console.log(error)
          InToast.info(error.message)
        }

        if (error.config.handleErrorSelf === true) {
          // gateway may return {Result, ResultMessage}
          return {
            result:
              error.response.data && error.response.data.Result
                ? error.response.data.Result
                : error.response.status,
            resultMessage:
              error.response.data && error.response.data.ResultMessage
                ? error.response.data.ResultMessage
                : error.response.statusText
          }
        } else {
          return Promise.reject(error)
        }
      }
    )
  }
  showLoading() {
    this.loadingCount++
    InLoading.show()
  }
  closeLoading() {
    this.loadingCount--
    if (this.loadingCount <= 0) {
      InLoading.close()
      this.loadingCount = 0
    }
  }
  request<T>(request: IRequest): Promise<IResponse<T>> {
    return new Promise<IResponse<T>>((resolve, reject) => {
      this.axiosInstance
        .request({ ...request, headers: { ...this.axiosRequest.headers, ...request.headers } })
        .then((res) => {
          resolve(res as unknown as IResponse<T>)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  post<T>(request: IRequest) {
    const postRequest = Object.assign(request, { method: 'post' })
    return this.request<T>(postRequest)
  }

  get<T>(request: IRequest) {
    const postRequest = Object.assign(request, { method: 'get' })
    return this.request<T>(postRequest)
  }
}

export const requestInstance = new Request({
  baseURL: '/gateway',
  timeout: TIMEOUT,
  headers: {
    'Accept-Language': LanguageEnum.ID,
    'X-TCF-APPID': '820001',
    'X-TCF-OS': 'android',
    'X-TCF-PACKAGENAME': 'com.indo.cashcerdas',
    'X-GW-PLATFORM': 'h5',
    'X-Request-Source': 'frontend',
    'X-App-Key': APP_KEY,
    'X-TCF-MERCHANT': TCF_MERCHANT,
    'X-TenantId': '1',
    'X-TCF-CHANNEL': 'google',
    'X-TCF-PRODUCT': 'CASHCERDAS'
  },
  hideLoading: false,
  hideToast: false
})
