<!DOCTYPE html>
<html lang="id">

<head>
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
</head>

<body style="width: 100%;height: 100%;">
  <div id="app"></div>
  <script type="module" src="./main.js"></script>

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-BEW1FD9JEZ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-BEW1FD9JEZ');
  </script>

  <!-- fingerprint -->
  <script>
    const fpPromise = import('https://openfpcdn.io/fingerprintjs/v4')
      .then(FingerprintJS => FingerprintJS.load())

    window.fpPromise = fpPromise
  </script>

  <!-- sensor -->
  <script charset="utf-8" src="../../../static/sensorsdata.min.1.24.4.js"></script>
  <% if (env.VITE_BUILD_TYPE==='release' ) { %>
    <script type="text/javascript">
      var sensors = window['sensorsDataAnalytic201505'];
      sensors.use('PageLeave');
      sensors.init({
        name: 'sensors',
        server_url: 'https://sensors.adakami.id/sa?project=cashcerdas_project',
        heatmap_url: 'https://static.adakami.id/libs/heatmap.min.1.24.4.js',
        app_js_bridge: true,
        use_app_track: true,
        show_log: false,
        heatmap: {
          scroll_notice_map: 'not_collect',
          clickmap: 'default',
          is_track_single_page() {
            return true;
          },
        },
      });
    </script>
    <!-- 性能探针 -->
    <script id="monitorJs" src="../../../static/ppdMonitor-sensor.js?<%= env.VITE_APPID %>" crossorigin
      data-params="method=get&appId=<%= env.VITE_APPID %>&buildType=2">
      </script>
    <% } else { %>
      <script type="text/javascript">
        var sensors = window['sensorsDataAnalytic201505'];
        sensors.use('PageLeave');
        sensors.init({
          name: 'sensors',
          server_url: 'https://sensors.adakami.id/sa?project=cashcerdas_project',
          heatmap_url: 'https://static.adakami.id/libs/heatmap.min.1.24.4.js',
          app_js_bridge: true,
          use_app_track: true,
          show_log: false,
          heatmap: {
            scroll_notice_map: 'not_collect',
            clickmap: 'default',
            is_track_single_page() {
              return true;
            },
          },
        });
      </script>
      <!-- eruda -->
      <script src="../../../static/eruda.min.js"></script>
      <script type="text/javascript">
        eruda.init();
      </script>
      <% } %>
</body>

</html>