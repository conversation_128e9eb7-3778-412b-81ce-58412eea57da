<!--
我们的目的是打开页面时已安装唤起应用，未安装跳转应用商店，不再让用户点击按钮手动触发。
目前测试下来：
1. 拷贝地址在浏览器里直接访问H5页面后无论是立刻还是delay，执行
   location.href = https://lite.cashcerdas.id/dl或 location.href = cashcerdas://lite.cashcerdas.id/dl
   都无法唤起app，但在页面中点击按钮时，上述两种链接都可以唤起app
   所以结论是只有用户实际触发才能唤起app，其他模拟行为都不行。因此直接在浏览里访问这种行为，我们不关心能否能唤起app，打开应用商店即可。

2. 当从外部应用如短信点击链接时，如果选择应用可以直接唤起，
   如果选择浏览器，只有cashcerdas://lite.cashcerdas.id/dl才可以唤起应用，https://lite.cashcerdas.id/dl 不能唤起应用
   所以这里才只能写cashcerdas://lite.cashcerdas.id/dl

3. 另外设置timeout是因为唤起应用有延迟或者过快，如果执行cashcerdas://lite.cashcerdas.id/dl后立刻 执行跳转应用商店，
   1）唤起过快：则可能会导致先打开应用，然后再打开应用商店，用户看到的是应用商店页面，返回才看到应用
   2）唤起延迟：则可能导致应用还没来得及唤起，就已离开跳转到应用商店了
   因此，综合测试下来延迟1.5s是一个折中的选择。

综上，所以我们只需要支持在外部应用点击链接时：
  1）已安装选择app可以直接唤起app；
  2）已安装选择浏览器可以通过浏览器直接唤起app；
  3）未安装打开浏览器可以访问应用商店地址
  4）直接拷贝在浏览器访问的不管了
-->
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
          name="viewport">
    <meta content="ie=edge" http-equiv="X-UA-Compatible">
    <title></title>
    <style type="text/css">
        html,
        body {
            display: flex;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        * {
            box-sizing: border-box;
        }

        a {
            text-decoration: none;
        }

        #view {
            display: block;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            position: absolute;
        }

        .center {
            margin: auto;
            width: 170px;
            padding: 220px 0;
            height: 50%;
            color: #333333;
            font-size: 14px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }

        .footer {
            width: 100%;
            height: 80px;
            position: absolute;
            bottom: 0;
            display: block;
            text-align: center;
        }

        .button {
            padding-left: 20px;
            padding-right: 20px;
            height: 40px;
            border-radius: 6px;
            background: #068BFD;
            font-size: 16px;
            color: #FFFFFF;
            text-align: center;
            line-height: 40px;
            position: absolute;
        }

        .inline {
            vertical-align: middle;
            display: inline-block;
        }

        .title {
            font-size: 30px;
            color: #000;
            text-align: center;
            margin-left: 15px;
        }

        .img {
            width: 46px;
            height: 46px;
        }


    </style>
  </head>
  <body>
    <script>
      const ua = navigator.userAgent.toLowerCase()
      const isAndroid = ua.indexOf('android') >= 0
      const isiOS = ua.indexOf('iphone') >= 0
      const timeout = 1500
      const andDownloadUrl = 'https://play.google.com/store/apps/details?id=com.indo.cashcerdas'
      const iosDownloadUrl = 'todo.no.ios.app.for.now' // when you have the ios app, replace with the app link, ex: https://apps.apple.com/id/app/yessscredit/id6503236559
      let downloadUrl = isiOS ? iosDownloadUrl : andDownloadUrl
      let openUrl = isiOS ? 'todo.no.ios.app.for.now' : 'cashcerdas://lite.cashcerdas.id/dl'

      const paramStr = location.search  //获取url中"?"符后的字串
      if (paramStr) {
        openUrl += paramStr //把原始链接中所有的参数都放到openUrl中
      }
      const referrerValue = getQueryString('referrer')
      if (referrerValue) {//判断是否存在referrer参数
        //将删除掉referrer参数后的deeplink url，encode后放到cashcerdas_dp_link_url参数后
        const deepLinkParam = '&cashcerdas_dp_link_url=' + encodeURIComponent(removeURLParameter('referrer'))
        //最后将原始的referrer参数值和encode后的deeplink url参数值拼在一起作为referrer的参数值拼接到download url后面
        const referrerParam = (isiOS ? '?' : '&') + 'referrer=' + encodeURIComponent(referrerValue + deepLinkParam)
        downloadUrl += referrerParam
      }
      //android进入页面后立刻发起跳转,ios因为需要拷贝，所以需要由用户点击按钮后触发拷贝和跳转
      if (!isiOS || !referrerValue) {
        openApp()
      }

      // 用户触发，手动唤起
      function openApp() {
        if (isiOS && referrerValue) {//iOS需要把链接拷贝到剪切板上，且只能由用户行为触发拷贝
          copyText(location.href)
        }
        if (isAndroid || isiOS) location.href = openUrl
        if (!isiOS) {
          const startTime = Date.now()
          setTimeout(function() {
            const endTime = Date.now()
            if ((startTime && (endTime - startTime) < (timeout + 200))) {
              location.href = downloadUrl
            } else {
              console.log('Wake up success.')
            }
          }, timeout)
        }
      }

      function getQueryString(name) {
        const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
        const r = window.location.search.substr(1).match(reg)
        if (r != null) {
          return decodeURIComponent(r[2])
        }
        return null
      }

      /**
       * 删除链接中的某个参数
       */
      function removeURLParameter(parameter) {
        const url = location.href
        const urlParts = url.split('?')
        if (urlParts.length >= 2) {
          //参数名前缀
          const prefix = encodeURIComponent(parameter) + '='
          const pars = urlParts[1].split(/[&;]/g)
          //循环查找匹配参数
          for (let i = pars.length; i-- > 0;) {
            if (pars[i].lastIndexOf(prefix, 0) !== -1) {
              //存在则删除
              pars.splice(i, 1)
            }
          }
          return urlParts[0] + (pars.length > 0 ? '?' + pars.join('&') : '')
        }
        return url
      }

      //兼容各类浏览器和的复制
      function copyText(text) {
        // 数字没有 .length 不能执行selectText 需要转化成字符串
        const textString = text.toString()
        let input = document.querySelector('#copy-input')
        if (!input) {
          input = document.createElement('input')
          input.id = 'copy-input'
          input.readOnly = 'readOnly'        // 防止ios聚焦触发键盘事件
          input.style.position = 'absolute'
          input.style.left = '-1000px'
          input.style.zIndex = '-1000'
          document.body.appendChild(input)
        }
        input.value = textString
        // ios必须先选中文字且不支持 input.select();
        selectText(input, 0, textString.length)
        if (document.execCommand('copy')) {
          document.execCommand('copy')
        }
        input.blur()
      }

      // input自带的select()方法在苹果端无法进行选择，所以需要自己去写一个类似的方法
      // 选择文本。createTextRange(setSelectionRange)是input方法
      function selectText(textbox, startIndex, stopIndex) {
        if (textbox.createTextRange) {//ie
          const range = textbox.createTextRange()
          range.collapse(true)
          range.moveStart('character', startIndex)//起始光标
          range.moveEnd('character', stopIndex - startIndex)//结束光标
          range.select()//不兼容苹果
        } else {//firefox/chrome
          textbox.setSelectionRange(startIndex, stopIndex)
          textbox.focus()
        }
      }
    </script>
    <div id="view">
      <div class="center">
        <a id="openLink" class="button" onClick="openApp()">Buka App CashCerdas</a>
      </div>
      <div class="footer">
        <div class="inline">
          <img class="img"
               src="./logo3.png"/>
        </div>
        <div class="inline title">CashCerdas</div>
      </div>
    </div>

  </body>
</html>
