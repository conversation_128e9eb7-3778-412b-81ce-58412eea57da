import Vue from 'vue'
import Router from 'vue-router'
import App from './App'
import './style.scss'
import '@/common/styles/reset.scss'
import '@/common/styles/font.scss'
import router from './router'
import pinia from './store'

import { PiniaVuePlugin } from 'pinia'

import { DeeplinkUrl } from '@/common/constants'
import { InCommonPlugin, TrackPlugin, InfiniteScroll } from '@/common/plugins'
import InfiniteLoading from '@nutui/nutui/dist/packages/infiniteloading/infiniteloading.js'
import '@nutui/nutui/dist/packages/infiniteloading/infiniteloading.scss'

Vue.use(InCommonPlugin, { router, deeplinkUrl: DeeplinkUrl })
document.title = 'CashCredas'

Vue.use(TrackPlugin, {}, router)
// Vue.use(InfiniteScroll)
Vue.use(Router)
Vue.use(PiniaVuePlugin)
Vue.use(pinia)

// @nutui/nutui-jdl已经使用了babel插件自动按需加载，且@nutui/nutui也是用这个插件但配置不一样，只能添加一份配置。所以@nutui/nutui只能使用手动按需加载咯
// import Radio from '@nutui/nutui/dist/packages/radio/radio.js'
// import '@nutui/nutui/dist/packages/radio/radio.scss'
// Radio.install(Vue)
// 直接使用 <nut-radio>备选项1</nut-radio>

InfiniteLoading.install(Vue)

import ScrollView from '@husandao/scroll-view'
import '@husandao/scroll-view/dist/style.css'
Vue.use(ScrollView)

import ClickOutside from '@/common/directive/vue-click-outside'
Vue.directive('click-outside', ClickOutside)

new Vue({
  el: '#app',
  router,
  pinia,
  render: (h) => h(App)
})
