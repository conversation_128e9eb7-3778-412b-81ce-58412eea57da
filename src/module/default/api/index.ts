import { requestInstance } from '../request/index'
import {
  IPagesHomeInfoRequest,
  IAccountOtpRequest,
  IAccountSmsRegisterOrLoginRequest,
  IUnionLoginRequest,
  IAdaCancelAccountRequest,
  IPopUpCompleteRequest
} from './request'
import {
  IPagesHomeInfoResponse,
  IAccountSmsRegisterOrLoginResponse,
  IUnionLoginResponse,
  IAdaFlxEntranceResponse,
  IAdaCancelAccountResponse,
  IPopUpShowFlagResponse
} from './response'

export const fetchPagesHomeInfo = async (req: IPagesHomeInfoRequest) => {
  return await requestInstance.post<IPagesHomeInfoResponse>({
    url: '/lm/pages/home',
    data: req
  })
}

export const fetchAccountOtp = async (req: IAccountOtpRequest) => {
  return await requestInstance.post({
    url: '/lm/module/account/otp',
    data: req
  })
}

export const fetchSmsRegisterOrLogin = async (req: IAccountSmsRegisterOrLoginRequest) => {
  return await requestInstance.post<IAccountSmsRegisterOrLoginResponse>({
    url: '/lm/module/account/smsRegisterOrLogin',
    data: req,
    hideToast: true,
    handleErrorSelf: true
  })
}

export const fetchAccountLogout = async () => {
  return await requestInstance.post({
    url: '/lm/module/account/logout'
  })
}

export const fetchUnionLogin = async (req: IUnionLoginRequest) => {
  return await requestInstance.post<IUnionLoginResponse>({
    url: '/lm/adaFlx/unionLogin',
    data: req
  })
}

export const fetchAdaFlxEntrance = async () => {
  return await requestInstance.post<IAdaFlxEntranceResponse>({
    url: '/lm/adaFlx/entrance'
  })
}

export const fetchCancelAccount = async (req: IAdaCancelAccountRequest) => {
  return await requestInstance.post<IAdaCancelAccountResponse>({
    url: '/lm/module/account/cancel',
    data: req
  })
}

export const savePushToken = async (token: string, allowPushMessage: boolean) => {
  return await requestInstance.post({
    url: '/lm/notification/push/saveToken',
    data: {
      token,
      allowPushMessage: allowPushMessage ? 1 : 0
    }
  })
}

export const recordPushLog = async (flowNo: string, eventName: number) => {
  return await requestInstance.post({
    url: '/lm/notification/push/event',
    data: {
      eventName,
      flowNo
    },
    hideToast: true,
    hideLoading: true
  })
}

export const recordFirstOpen = async (isFirstOpen: boolean) => {
  return await requestInstance.post({
    url: '/lm/track/event',
    data: {
      eventName: 'openApp',
      body: {
        isFirstOpen
      }
    }
  })
}

export const popUpShowFlag = async () => {
  return await requestInstance.post<IPopUpShowFlagResponse>({
    url: '/lm/pages/popUpShowFlag'
  })
}

export const popUpComplete = async (req: IPopUpCompleteRequest) => {
  return await requestInstance.post<boolean>({
    url: '/lm/pages/popUpComplete',
    data: req
  })
}
