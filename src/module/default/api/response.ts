export type IPagesHomeInfoResponse = {
  dispAdaFlxFakeCard: boolean
  loanCards: ILoanCards
  maxLoanAmount: string
  /**
   * 是否命中风险标签(自然流量戳额被拒)。
   */
  isFirstReject?: boolean
}

export type IAccountSmsRegisterOrLoginResponse = {
  openId: string
  userName: string
  mobile: string
  token: string
}

export type IUnionLoginResponse = {
  openId: string
  userName: string
  mobile: string
  token: string
}

export type ILoanCards = {
  adaFlx: IAdaFlx
  loanMarket: ILoanMarket[]
}

export type IAdaFlx = {
  quotaCard: {
    status: number
    availableAmount: number
    totalAmount: number
    isQuotaEnough: boolean
    quotaTips: number
    recentExpireTime: number
  }
  loanCard: {
    loanList: {
      listId: number
      applyAmount: number
      applyTime: number
      lifeCycleCode: string
      needSign: boolean
      lifeCycleCodeTitle: string
      lifeCycleCodeStr: string
      serialNo: string
    }[]
  }
  repaymentCard: {
    loanList: IRepaymentCardLoan[]
  }
}

export type ILoanMarket = {
  id: string
  tenantId: string
  name: string
  icon: string
  description: string
  tags: string
  minAmount: string
  maxAmount: string
  minDayInterestRate: string
  maxDayInterestRate: string
  minDayLoanPeriod: string
  maxDayLoanPeriod: string
  passingRate: string
  rating: string
  loanUserCount: string
}

export type IRepaymentCardLoan = {
  listId: number
  applyAmount: number
  applyTime: number
  lifeCycleCode: string
  needSign: boolean
  lifeCycleCodeTitle: string
  lifeCycleCodeStr: string
  debtId: number
  dueDate: number
  overdueDay: number
  owingAmount: number
  serialNo: string
}

export type IAdaFlxEntranceResponse = {
  url: string
}

export type IAdaCancelAccountResponse = {
  cancelResult: boolean
  flowNo: string
  freezePeriodTime: number
}

export type IPopUpShowFlagResponse = {
  showFlag: boolean
  popupId: number
}
