export type IPagesHomeInfoRequest = {
  sortByString: string
  orderString: string
}

export type IAccountOtpRequest = {
  mobile: string
  verifyType?: number
  msgType?: number
}

export type IAccountSmsRegisterOrLoginRequest = {
  mobile: string
  verifyCode: string
}

export type IUnionLoginRequest = {
  externalToken: string
}

export type IAdaCancelAccountRequest = {
  mobile: string
  verifyCode: string
  reason?: string
}

export type IPopUpCompleteRequest = {
  popupId: number
}
