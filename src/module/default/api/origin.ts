import { requestInstance } from '../request/origin'

export const fetchP2pList = async () => {
  return await requestInstance.get<fetchP2pHomeBannerItem[]>({
    url: '/loan-channels/p2p/custom?listType=MAIN'
  })
}

export type fetchP2pHomeBannerItem = {
  id: number
  name: string
  icon: string
  description: string
  tags: [string]
  minAmount: number
  maxAmount: number
  minDayInterestRate: number
  maxDayInterestRate: number
  minDayLoanPeriod: number
  maxDayLoanPeriod: number
  passingRate: number
  rating: number
  loanUserCount: number
}
export const fetchP2pHomeBannerList = async () => {
  return await requestInstance.get<fetchP2pHomeBannerItem[]>({
    url: '/loan-channels/p2p/custom?listType=HOME_BANNER'
  })
}
