import { defineStore } from 'pinia'
import { useBridge } from '@/common/bridge'
import { savePushToken } from '@/module/default/api/index'
const useUserInfoStore = defineStore('userInfoStore', {
  persist: {
    storage: localStorage
  },
  state: () => ({
    openId: '',
    username: '',
    mobile: '',
    token: ''
  }),
  actions: {
    saveInfo(openId: string, username: string, mobile: string, token: string) {
      this.openId = openId
      this.username = username
      this.mobile = mobile
      this.token = token
      window.sensors.login(openId)
      // update firebase token
      const bridge = useBridge()
      bridge.getFirebasePushToken().then((res) => {
        if (res && res.token) {
          savePushToken(res.token, res.notificationOn)
        }
      })
    },
    clearInfo() {
      this.openId = ''
      this.username = ''
      this.mobile = ''
      this.token = ''
    },
    saveToken(token: string) {
      this.token = token
    }
  },
  getters: {
    isLogin(): boolean {
      return !!this.token
    },
    getMobile(): string {
      return this.mobile
    }
  }
})

export default useUserInfoStore
