import { defineStore } from 'pinia'

const useNativeInfoStore = defineStore('nativeInfoStore', {
  state: () => ({
    deviceInfo: { aid: '', gaid: '', osVersion: '', model: '' },
    appInfo: { version: '', firebaseInstanceId: '' },
    appsflyerInfo: { afUid: '', mediaSource: '', campaign: '', agency: '', adsetId: '' }
  }),
  actions: {
    clearInfo() {
      this.deviceInfo = { aid: '', gaid: '', osVersion: '', model: '' }
      this.appInfo = { version: '', firebaseInstanceId: '' }
      this.appsflyerInfo = { afUid: '', mediaSource: '', campaign: '', agency: '', adsetId: '' }
    },

    setDeviceInfo(data: { aid: string; gaid: string; osVersion: string; model: string }) {
      this.deviceInfo = data
    },

    setAppInfo(data: { version: string; firebaseInstanceId: string }) {
      this.appInfo = data
    },

    setAppsflyerInfo(data: {
      afUid: string
      mediaSource: string
      campaign: string
      agency: string
      adsetId: string
    }) {
      this.appsflyerInfo = data
    }
  }
})

export default useNativeInfoStore
