import { InLoading } from '@/common/components/InLoading'
import { InToast } from '@/common/components/InToast'
import { loadScriptString, versionCopamre } from '@/common/utils'
import { getQueryString } from '@/common/utils'

function isMock() {
  return getQueryString('mock') === '1'
}

class OppoService {
  #sdkLoadDone = false
  #sdkLoadPromise = false
  #errorCount = 0

  DEEP_LINK_SCAN = 'finlife://finlife/zxing/scan'

  constructor() {
    if (!this.#sdkLoadDone) {
      this.#loadOppoSDK()
    }
  }

  async #loadOppoSDK() {
    InLoading.show()
    this.#sdkLoadPromise = loadScriptString(
      '//rwallet-sg.finosfin.com/common-ocs/jslib/jsapi.min.js?v=6'
    ).then(() => {
      InLoading.close()
      this.#sdkLoadDone = true
    })
    return this.#sdkLoadPromise
  }

  getAppVersion() {
    const uaMatch = navigator.userAgent.match(/fintech_IN\/(\d+\.?)+/)
    const appVersion = uaMatch && uaMatch[0].replace('fintech_IN/', '')
    console.log(appVersion)
    return appVersion || ''
  }

  versionAvailable(version = '1.0.0') {
    return versionCopamre(this.getAppVersion(), version) >= 0
  }

  invoke(cls, method, param = {}) {
    return this.#sdkLoadPromise
      .then(() => {
        return new Promise((r) => {
          window.RainbowBridge.invoke(cls, method, param, (data) => {
            r(
              data || {
                data: null,
                status: { code: -999 }
              }
            )
          })
        })
      })
      .catch((error) => {
        console.error(error)
        this.#errorCount++
        if (this.#errorCount > 3) return
        return this.#loadOppoSDK().then(() => {
          return this.invoke(cls, method, param)
        })
      })
  }

  /**
   * @description: 金融账户授权
   * @param {*} param
   * @return {*}
   */
  getAuthorizedCode(param) {
    // {
    //   appId: '', // 应用id
    //   scope: '', // 授权范围,使用逗号分隔
    //   appPkg: '', // 请求授权的应用包名，H5时可以为空
    //   state: '', // 状态
    //   timeStamp: new Date(), // 时间戳 毫秒
    //   sign: '', // 签名
    // }
    if (isMock()) {
      return Promise.resolve({
        data: { authCode: 'its mock' },
        status: { code: 0 }
      })
    }
    return this.invoke('JSCommondMethod', 'reqOAuthCode', param)
  }

  /**
   * @description: 通过相机/相册获取图片
   * @param {*} param
   * @return {*}
   */
  getPhoto(param) {
    // {
    //   takeType:Number, // 0（选择相册）和1（拍照）
    //   quality:Number, // 默认100
    //   compressFormat:Number, // 压缩方式 0:jpeg(默认) 1:png 2:webp
    //   },
    return this.invoke('JSCommondMethod', 'getPhoto', param)
  }

  /**
   * @description: 扫二维码、扫imei条形码
   * @return {*}
   */
  getScanBarCode(param = {}) {
    return this.invoke('JSCommondMethod', 'getScanBarCode', param)
  }

  /**
   * @description: 埋点上报
   * @param {*} param
   * @return {*}
   */
  track(pageId, eventId, param = {}) {
    // {
    //   category: String, // 业务id
    //   eventId: String, // eventId
    //   pageId: String, // pageId
    //   infoMap: object, // 埋点信息
    // },
    return this.invoke('JSCommondMethod', 'reportEventId', {
      category: 900001,
      pageId,
      eventId,
      infoMap: param
    })
  }

  trackClick(pageId, param = {}) {
    return this.track(pageId, 7201, param)
  }

  trackImp(pageId, param = {}) {
    return this.track(pageId, 7002, param)
  }

  /**
   * @description: 获取设备信息
   * 返回品牌、手机型号、Google广告ID
   * @return {*}
   */
  getDeviceInfo() {
    return this.invoke('JSCommondMethod', 'getFqDevInfo')
  }

  /**
   * @description: 颁发临时登陆凭证code
   * @param {*} appId
   * @return {*}
   */
  getOAuthLoginCode(appId) {
    if (isMock()) {
      return Promise.resolve({
        data: { code: 'its mock' },
        status: { code: 0 }
      })
    }
    return this.invoke('JSCommondMethod', 'getOAuthLogin', {
      appId
    })
  }

  /**
   * @description: 获取设备位置信息
   * @return {*}
   */
  getDeviceGPSInfo(param = {}) {
    // // 成功返回值：
    // {
    //   provider: String, // 定位选项
    //   longitude: Number,// 经度 113.8937718
    //   latitude: Number, // 纬度 22.5346912
    // }
    // // 失败返回值——权限拒绝：
    // {
    // pmsState:Number // 1（拒绝）和 2（永久拒绝）
    // }
    // // 失败返回值——位置服务未打开：
    // {
    // pmsState:Number // 3
    // }
    // // 失败返回值——定位获取超时或失败：
    // {
    // provider: String, // 定位选项
    // longitude: Number,// 经度 0
    // latitude: Number, // 纬度 0
    // pmsState: Number, // 4
    // }
    return this.invoke('JSInterfaceMethod', 'getDeviceGPSInfo', {
      provider: 'network',
      timeOut: 1000,
      ...param
    })
  }

  /**
   * @description: 关闭webview
   * @return {*}
   */
  close() {
    return this.invoke('JSCommondMethod', 'onFinish')
  }

  /**
   * @description: 打开设置页
   * @return {*}
   */
  openSetting() {
    return this.invoke('JSInterfaceMethod', 'useNativePermission', {
      actionType: '3'
    })
  }

  /**
   * @description: 人脸活体验证
   * @param {*} bizNo 此次活体唯一标识
   * @return {*}
   */
  liveFace(bizNo) {
    return this.invoke('JSInterfaceMethod', 'faceAuth', {
      // meglive：动作活体
      // still：静默活体
      // flash：炫彩活体，通过打光进行活体验证，炫彩活体相较于静默活体安全性更高，但通过率会略有降低
      // raw_image：不进行活体验证，仅使用上传的图片进行后续的比对
      livenessType: 3, // 1、meglive, 2、still, 3、flash, 4、raw_image
      bizNo,
      business: 'AdaKami'
    })
  }

  canLiveFace() {
    if (!this.versionAvailable('1.8.61')) {
      InToast.info('Silakan unduh aplikasi Fineasy versi terbaru')
      return false
    }
    return true
  }

  linkToScan() {
    location.href = this.DEEP_LINK_SCAN
  }

  /**
   * @description: 根据策略获取经纬度
   * @param {*} config.provider gps、network
   * @param {*} count
   * @return {*}
   */
  async getGPSInfoByStrategy(config, count = 1) {
    return new Promise(async (r, s) => {
      const { status, data } = await this.getDeviceGPSInfo({
        timeOut: 5000,
        ...config
      })
      console.log({
        status,
        data
      })
      // 有权限
      // 老版本即使失败/超时，status.code也是0， 返回的经纬度为0,0
      // 新版本获取失败/超时，status.code会是1
      if (status.code === 0) {
        // 真-成功
        if (data.latitude != 0 || data.longitude != 0) {
          r(data)
        } else if (count > 2) {
          // 最多获取三次，还是00就直接提交
          r(data)
        } else {
          r(await this.getGPSInfoByStrategy(config, count + 1))
        }
      } else if (status.code === 1) {
        if (data?.pmsState === 4) {
          if (count > 2) {
            // 最多获取三次，还是00就直接提交
            r(data)
          } else {
            r(await this.getGPSInfoByStrategy(config, count + 1))
          }
        }
      } else {
        // 没有权限
        s()
      }
    })
  }

  /**
   * @description: GPS/Network连续获取5个，最大获取10次，10次获取不到不再获取；用户在页面流程中连续获取，离开页面不再获取
   * @param {*} config
   * @param {*} singleCallback
   * @return {*}
   */
  startGetGPSInfoStrategy(config, singleCallback) {
    const UPPER_LIMMIT = 5
    const UPPER_COUNT = 10
    const networkResult = []
    const gpsResult = []

    const fetchGPS = async (provider, result = []) => {
      let count = 0
      while (count < UPPER_COUNT && result.length < UPPER_LIMMIT) {
        const { status, data } = await this.getDeviceGPSInfo({
          timeOut: 5000,
          provider,
          ...config
        })
        console.log({ status, data })
        if (status.code === 0) {
          // 真-成功
          if (data.latitude != 0 || data.longitude != 0) {
            result.push(data)
            singleCallback && singleCallback({ ...data, provider })
          }
        }
        count++
      }
      return result
    }

    fetchGPS('network', networkResult)
    fetchGPS('gps', gpsResult)
    return {
      getResult() {
        return networkResult.concat(gpsResult)
      }
    }
  }
}

export const oppoService = new OppoService()

export class OppoGPSService {
  constructor() {}
}
