// import { oppoService } from './OppoService'
import { createDialog } from '@/common/components/InDialog'
import PopupMenu from '@/common/components/InForm/InDropdown/PopupMenu.vue'

export function base64toFile(base64, name) {
  let arr = base64.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], name, {
    type: mime
  })
}

export const getPhotoFileFromOppo = async (extraParam = {}, takeType, name = 'tmp.jpeg') => {
  if (takeType === void 0) {
    return new Promise(async (r) => {
      createDialog({
        component: PopupMenu,
        options: [
          { label: 'Galeri foto', value: 0 },
          { label: 'Foto', value: 1 }
        ],
        title: '',
        onClose: () => {
          //oppoService.trackClick(1094, { button_id: 3 })
        },
        onMounted: () => {
          //oppoService.trackImp(1094)
        }
      }).onOk(async (choosedTakeType, close) => {
        console.log(choosedTakeType)
        //oppoService.trackClick(1094, { button_id: choosedTakeType.value == 0 ? 1 : 2 })
        const result = await oppoGetPhotoFile(choosedTakeType.value, extraParam, name)
        close()
        r(result)
      })
    })
  } else {
    return oppoGetPhotoFile(takeType, extraParam, name)
  }
}

const oppoGetPhotoFile = async (type, extraParam, name = 'tmp.jpeg') => {
  // const { data, status } = await oppoService.getPhoto({
  //   takeType: type, // 0（选择相册）和1（拍照）
  //   quality: 50, // 默认100
  //   compressFormat: 'jpeg', // 压缩方式 0:jpeg(默认) 1:png 2:webp
  //   ...extraParam
  // })
  // if (status.code === 0 && data.photo) {
  //   const photo = data.photo
  //   const base64Img = `data:image/jpeg;base64,${photo}`
  //   const file = base64toFile(base64Img, name)
  //   console.log(file.size)
  //   return { file, base64Img }
  // }
  // return null
}
