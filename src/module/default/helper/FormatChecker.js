import { testPattern } from '@/common/utils/patterns'

export class FormatChecker {
  MAX_LENGTH = 50

  constructor() {}

  getTrimedText(text) {
    return text?.trim() ?? text
  }

  checkRequired(text) {
    const _text = this.getTrimedText(text)
    if (_text === void 0 || _text === null || _text === '') {
      return 'Tidak boleh kosong'
    } else {
      return true
    }
  }

  check(text) {
    return this.checkRequired(text)
  }

  get formValidator() {
    return {
      rules: [(text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

export class FormatCheckerName extends FormatChecker {
  MAX_LENGTH = 50

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return (
      testPattern.isName(_text) ||
      'Nama harus alphabet, contoh <PERSON>cy <PERSON>, silakan masukkan konten yang benar.'
    )
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

export class FormatCheckerMobile extends FormatChecker {
  MAX_LENGTH = 13

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return (
      testPattern.isMobile(_text) || 'Harap isi 10-13 digit nomor ponsel yang benar diawali dengan angka 08'
    )
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

export class FormatCheckerSecondPhoneOptional extends FormatCheckerMobile {
  constructor() {
    super()
  }

  check(text) {
    if (!text) return true
    else return super.check(text)
  }

  get formValidator() {
    return {
      rules: [/*(text) => this.checkRequired(text),*/ (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

export class FormatCheckerCompayPhoneOptional extends FormatChecker {
  MAX_LENGTH = 20

  constructor() {
    super()
  }

  check(text) {
    if (!text) return true
    const _text = this.getTrimedText(text)
    return testPattern.isCompanyPhone(_text) || 'Jumlah nomor telepon 1-20 angka'
  }

  get formValidator() {
    return {
      rules: [/*(text) => this.checkRequired(text), */ (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

export class FormatCheckerEducation extends FormatChecker {
  constructor() {
    super()
  }
}

export class FormatCheckerEmail extends FormatChecker {
  MAX_LENGTH = 60

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return testPattern.isEmail(_text) || 'Format alamat email Anda salah, mohon isi alamat email yang benar.'
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: 婚姻状况
 * @return {*}
 */
export class FormatCheckerWedding extends FormatChecker {
  constructor() {
    super()
  }
}

/**
 * @description: 母亲的姓氏
 * @return {*}
 */
export class FormatCheckerLastNameOfMother extends FormatChecker {
  MAX_LENGTH = 50

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return testPattern.isName(_text) || 'Harap masukkan nama belakang yang terdiri dari 2-50 karakter'
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: 贷款原因
 * @return {*}
 */
export class FormatCheckerBorrowTarget extends FormatChecker {
  constructor() {
    super()
  }
}

/**
 * @description: 居住状态
 * @return {*}
 */
export class FormatCheckerLivingState extends FormatChecker {
  constructor() {
    super()
  }
}

/**
 * @description: 住址
 * @return {*}
 */
export class FormatCheckerLivingAddress extends FormatChecker {
  MAX_LENGTH = 200

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return testPattern.isValidChar(_text) || 'Format alamat tidak benar, silakan masukkan konten yang benar'
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: 工作类型
 * @return {*}
 */
export class FormatCheckerJobType extends FormatChecker {
  constructor() {
    super()
  }
}

/**
 * @description: 公司名称
 * @return {*}
 */
export class FormatCheckerCompanyName extends FormatChecker {
  MAX_LENGTH = 100

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return (
      testPattern.isValidChar(_text) ||
      'Format nama perusahaan tidak benar, silakan masukkan konten yang benar.'
    )
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: 月收入
 * @return {*}
 */
export class FormatCheckerMonthlyIncome extends FormatChecker {
  MAX_LENGTH = 9

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return (
      (/^[1-9]\d{0,8}$/.test(_text) && !_text.startsWith('0')) ||
      'Format pada pendapatan per bulan salah, silakan masukkan konten yang benar.'
    )
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: 工作年限
 * @return {*}
 */
export class FormatCheckerWorkAge extends FormatChecker {
  constructor() {
    super()
  }
}

/**
 * @description: 联系人关系
 * @return {*}
 */
export class FormatCheckerContactRelation extends FormatChecker {
  constructor() {
    super()
  }
}

/**
 * @description: NPWP
 * @return {*}
 */
export class FormatCheckerNPWP extends FormatChecker {
  MAX_LENGTH = 15

  constructor() {
    super()
  }

  check(text) {
    if (!text) return true
    const _text = this.getTrimedText(text)
    return /^\d{15}$/.test(_text) || 'NPWP harus 15 angka.'
  }

  get formValidator() {
    return {
      rules: [(text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: Ktp姓名
 * @return {*}
 */
export class FormatCheckerKtpName extends FormatChecker {
  MAX_LENGTH = 50

  constructor() {
    super()
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return testPattern.isName(_text) || 'Format tidak benar, harus diantara 2-50 karakter.'
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: Ktp号码
 * @return {*}
 */
export class FormatCheckerKtpNumber extends FormatChecker {
  MAX_LENGTH = 16

  constructor() {
    super()
  }

  isAllSameChar(str) {
    if (!str) return false
    for (let i = 0; i < str.length; i++) {
      for (let j = i + 1; j < str.length; j++) {
        if (str[i] != str[j]) {
          return false
        }
      }
    }
    return true
  }

  check(text) {
    const _text = this.getTrimedText(text)
    return (
      (/^\d{16}$/.test(_text) && !this.isAllSameChar(_text)) ||
      'Format tidak benar, silakan masukkan kembali.'
    )
  }

  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

export class FormatCheckerBankName extends FormatChecker {
  MAX_LENGTH = 50
  constructor() {
    super()
  }
  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: 银行卡
 * @return {*}
 */
export class FormatCheckerBankNumber extends FormatChecker {
  MAX_LENGTH = 20
  constructor() {
    super()
  }
  check(text) {
    const _text = this.getTrimedText(text)
    return /^\d{8,16}$/.test(_text) || 'Format nomor rekening tidak benar.'
  }
  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 * @description: 省+市+区
 * @return {*}
 */
export class FormatCheckerSSQ extends FormatChecker {
  MAX_LENGTH = 100

  constructor() {
    super()
  }
}

/**
 * @description: 发薪日
 * @return {*}
 */
export class FormatCheckerPayday extends FormatChecker {
  constructor() {
    super()
  }
}

/**
 * 学校
 */
export class FormatCheckerSchoolOptional extends FormatChecker {
  MAX_LENGTH = 200

  constructor() {
    super()
  }

  check(text) {
    if (!text) return true
    const _text = this.getTrimedText(text)
    return testPattern.isSchool(_text) || 'Karakter selain huruf, angka, dan spasi tidak dapat dimasukkan'
  }

  get formValidator() {
    return {
      rules: [/*(text) => this.checkRequired(text),*/ (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/* @description: otp
 * @return {*}
 */
export class FormatCheckerOtp extends FormatChecker {
  MAX_LENGTH = 6
  constructor() {
    super()
  }
  check(text) {
    const _text = this.getTrimedText(text)
    return /^\d{6}$/.test(_text) || 'Format tidak benar'
  }
  get formValidator() {
    return {
      rules: [(text) => this.checkRequired(text), (text) => this.check(text)],
      maxLength: this.MAX_LENGTH
    }
  }
}

/**
 *  专业
 */
export class FormatCheckerMajorOptional extends FormatCheckerSchoolOptional {}

export const formatChecker = {
  Otp: new FormatCheckerOtp(),
  Name: new FormatCheckerName(),
  Mobile: new FormatCheckerMobile(),
  Education: new FormatCheckerEducation(),
  Email: new FormatCheckerEmail(),
  Wedding: new FormatCheckerWedding(),
  LastNameOfMother: new FormatCheckerLastNameOfMother(),
  BorrowTarget: new FormatCheckerBorrowTarget(),
  LivingState: new FormatCheckerLivingState(),
  LivingAddress: new FormatCheckerLivingAddress(),
  JobType: new FormatCheckerJobType(),
  CompanyName: new FormatCheckerCompanyName(),
  MonthlyIncome: new FormatCheckerMonthlyIncome(),
  WorkAge: new FormatCheckerWorkAge(),
  ContactRelation: new FormatCheckerContactRelation(),
  NPWP: new FormatCheckerNPWP(),
  KtpName: new FormatCheckerKtpName(),
  KtpNumber: new FormatCheckerKtpNumber(),
  BankNumber: new FormatCheckerBankNumber(),
  BankName: new FormatCheckerBankName(),
  SSQ: new FormatCheckerSSQ(),
  Payday: new FormatCheckerPayday(),
  CompanyPhone: new FormatCheckerCompayPhoneOptional(),
  SecondPhone: new FormatCheckerSecondPhoneOptional(),
  School: new FormatCheckerSchoolOptional(),
  Major: new FormatCheckerMajorOptional()
}
