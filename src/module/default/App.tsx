import { defineComponent, onMounted } from 'vue'
import { recordFirstOpen, recordPushLog } from './api'
import { localStorage } from '../../common/utils/storage'
import { CashCerdsOpenFlag } from './constant'
import { useBridge } from '@/common/bridge'
import useNativeInfoStore from '@/module/default/store/modules/nativeInfo'

export default defineComponent({
  setup() {
    const bridge = useBridge()
    const nativeInfoStore = useNativeInfoStore()
    nativeInfoStore.clearInfo()
    Promise.all([
      bridge.getAppInfo().then((res) => {
        nativeInfoStore.setAppInfo(res)
      }),
      bridge.getDeviceInfo().then((res) => {
        nativeInfoStore.setDeviceInfo(res)
      }),
      bridge.getAppsflyerInfo().then((res) => {
        nativeInfoStore.setAppsflyerInfo(res)
      })
    ]).then(() => {
      const hasOpen = localStorage.get(CashCerdsOpenFlag)
      recordFirstOpen(hasOpen ? false : true)
      if (!hasOpen) {
        localStorage.set(CashCerdsOpenFlag, true)
      }
      checkPushLog()
    })

    // get appsflyerInfo again to confirm info
    setTimeout(() => {
      bridge.getAppsflyerInfo().then((res) => {
        nativeInfoStore.setAppsflyerInfo(res)
      })
    }, 3000)
    bridge.onShow(() => {
      checkPushLog()
    })
    const checkPushLog = () => {
      setTimeout(() => {
        bridge.getPushLog().then((res) => {
          if (res && res.length > 0) {
            res.forEach((item) => {
              recordPushLog(item.flowNo, item.event === 'arrive' ? 1 : 2)
            })
          }
        })
      }, 1000)
    }
    onMounted(() => {})
    return () => {
      return (
        <div style={{ width: '100%', height: '100%' }}>
          <router-view />
        </div>
      )
    }
  }
})
