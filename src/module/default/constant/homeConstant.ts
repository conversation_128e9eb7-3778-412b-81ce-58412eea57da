export enum RepaymentCardEnum {
  // 待还款
  PENDING_REPAYMENT = 1,
  // 当天待还
  PENDING_REPAYMENT_TODAY,
  // 逾期
  OVERDUE
}

export const getRepaymentCardEnumText = (type: RepaymentCardEnum) => {
  switch (type) {
    case RepaymentCardEnum.PENDING_REPAYMENT:
      return '待还款'
    case RepaymentCardEnum.PENDING_REPAYMENT_TODAY:
      return '当天待还'
    case RepaymentCardEnum.OVERDUE:
      return '逾期'
    default:
      return ''
  }
}

export enum HomeCardEnum {
  // 授信前/未登录/未戳额
  BEFORE_CREDIT = 1, //#
  // 放款中
  PENDING_REPAYMENT_TODAY, //#
  // 待签名
  WAITING_FOR_SIGNATURE, //#
  // 放款失败
  LOAN_FAILURE,
  // 可借额度
  LOAN_AMOUNT, //#
  // 额度不足
  INSUFFICIENT_AMOUNT,
  // 额度过期
  CREDIT_LIMIT_EXPIRED,
  // 戳额中
  OBTAINING_QUOTA
}

export const getHomeCardEnumText = (cardEnum: HomeCardEnum) => {
  switch (cardEnum) {
    case HomeCardEnum.BEFORE_CREDIT:
      return '未登录'
    case HomeCardEnum.PENDING_REPAYMENT_TODAY:
      return '放款中'
    case HomeCardEnum.WAITING_FOR_SIGNATURE:
      return '待签名'
    case HomeCardEnum.LOAN_FAILURE:
      return '放款失败'
    case HomeCardEnum.LOAN_AMOUNT:
      return '可借额度'
    case HomeCardEnum.INSUFFICIENT_AMOUNT:
      return '额度不足'
    case HomeCardEnum.CREDIT_LIMIT_EXPIRED:
      return '额度过期'
    case HomeCardEnum.OBTAINING_QUOTA:
      return '戳额中'
    default:
      return ''
  }
}

export enum LiveCycleCodeEnum {
  /**
   * 审核中
   */
  AUDIT = 'AUDIT',

  /**
   * 审核成功待放款，募集资金中
   */
  PROCESSING = 'PROCESSING',

  /**
   * 募集资金完成，提现中
   */
  WITHDRAWING = 'WITHDRAWING',

  /**
   * 标的被拒绝
   */
  REJECT = 'REJECT',

  /**
   * 提现成功等待还款， 有debt
   */
  UNREPAY = 'UNREPAY',

  /**
   * 标的所有债务还清， 有debt
   */
  FINISH = 'FINISH',

  /**
   * 提现失败，后台核销标的
   */
  FINISH_4WITHDRAW_FAIL = 'FINISH_4WITHDRAW_FAIL',

  /**
   * 取消
   */
  ALL_CANCEL = 'ALL_CANCEL'
}

export enum QuotaCardStatusEnum {
  NO_QUOTA_OBTAINED = 0,
  GETTING_QUOTA,
  OBTAINED_QUOTA_SUCCESSFULLY
}
