import type { AxiosRequestConfig } from 'axios'

export const TIMEOUT = 30 * 1000
export const HEADER_LN = 'in'
export const TCF_MERCHANT = 'TCF_H5'
export const TCF_APPID = '900006'
export const APP_KEY = 'I4GcTC3NLBsfTlxYkU7J1OfJtbbnwGQO'
export const APP_SECRET =
  'HJSJZdIiIbCPHAVYplTSoMxbSFJzyrkcwoxZQZupnJFpdiGaEqtMETCSfeHAPaEaiOwTMIOheuoppQBWmKauBaLdOhQuBzaSpENGrfvwjuDYMvvAYubZfjuBetTqnbzq'

export enum LanguageEnum {
  EN = 'en',
  ID = 'id'
}

export interface IResponse<T> {
  result: number
  resultMessage: string
  content: T
}

export interface IRequest extends AxiosRequestConfig {
  hideLoading?: boolean
  hideToast?: boolean
  handleErrorSelf?: boolean
}
