{"name": "my-vue-app", "private": true, "version": "2.1.0", "scripts": {"buildModule": "vue-tsc && cross-env VITE_BUILD_TYPE=$npm_config_build_type cross-env VITE_MODULE=$npm_config_module vite build && node ./script/startLog.mjs", "build": "node ./script/buildAll.mjs", "build:default": "npm run buildModule --module=default", "lint": "eslint --ext .js,.jsx,.ts,.tsx,.d.ts,.vue src/ --fix", "lint:css": "stylelint src/**/*.{css,scss,sass,less,vue} --fix", "i": "npm i --registry=http://registry.npmmirror.com", "//": "/////////////////////", "devModule": "cross-env VITE_MODULE=$npm_config_module vite", "dev": "vite", "dev:default": "npm run devModule --module=default", "//1": "vue-tsc做语法检查，vite build编译打包js", "//2": "\"build\": \"vue-tsc && vite build\",", "preview": "vite preview"}, "dependencies": {"@better-scroll/core": "2.4.2", "@better-scroll/pull-down": "2.4.2", "@better-scroll/pull-up": "2.4.2", "@husandao/scroll-view": "^1.0.3", "@nutui/nutui": "^2.3.13", "@nutui/nutui-jdl": "^1.0.5", "abortcontroller-polyfill": "^1.7.6", "axios": "^1.4.0", "clipboard": "^2.0.11", "connect-history-api-fallback": "^2.0.0", "dayjs": "^1.11.8", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "3.2", "rxjs": "^7.8.1", "swiper": "^5.4.5", "v-focus-next": "^1.0.4", "v-money": "^0.8.1", "vue": "^2.7.14", "vue-awesome-swiper": "^4.1.1", "vue-i18n": "^8.28.2", "vue-router": "^3.6.5", "zustand": "^4.3.9"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.22.5", "@nutui/babel-plugin-separate-import": "^1.2.2", "@types/lodash": "^4.17.0", "@types/node": "^18.16.2", "@types/shelljs": "^0.8.12", "@types/swiper": "^6.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vanilla-extract/css": "^1.12.0", "@vanilla-extract/vite-plugin": "^3.8.2", "@vitejs/plugin-legacy": "^3.0.1", "@vitejs/plugin-vue2": "^2.2.0", "@vitejs/plugin-vue2-jsx": "^1.1.0", "app-root-path": "^3.1.0", "babel-plugin-import": "^1.13.6", "cheerio": "^1.0.0-rc.12", "cross-env": "^7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.4.0", "js-sha256": "^0.11.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.3.2", "rimraf": "^3.0.2", "sass": "1.32.13", "shelljs": "^0.8.5", "stylelint": "^15.10.2", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^10.0.0", "typescript": "^4.9.3", "typescript-plugin-css-modules": "^5.0.1", "vite": "^4.0.0", "vite-plugin-babel": "^1.1.3", "vite-plugin-ejs": "^1.6.4", "vite-plugin-prerender": "^1.0.8", "vite-plugin-static-copy": "^0.17.1", "vue-eslint-parser": "^8.3.0", "vue-property-decorator": "^9.1.2", "vue-tsc": "^1.0.11", "vue-tsx-support": "^3.2.0"}, "//": "当开发者没有使用volta时，以下engines和npmrc strict配置 也会在 npm install时再拦截一遍", "engines": {"node": "16.20.2"}, "volta": {"node": "16.20.2", "yarn": "1.22.19", "//1": "volta也支持统一管理yarn版本号", "//2": "老工程使用node14.17.0，新工程使用16.20.2而不使用18.*的原因是Aladdin构建环境centos版本太低无法安装18"}}