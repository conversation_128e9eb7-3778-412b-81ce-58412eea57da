{
  "compilerOptions": {
    "module": "ES2022",
    "noEmit": true,
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "jsx": "preserve",
    // 添加了typescript-plugin-css-modules以后，ts和tsx 不再识别vue文件，两个方案1为每个vue添加一个js文件，2在vite-env.d.ts中添加declare
    // 添加了typescript-plugin-css-modules以后还需要在VSCode配置中添加如下配置才能实现css代码提示
    // "typescript.tsserver.pluginPaths": [
    //   "typescript-plugin-css-modules"
    // ],
    "plugins": [{"name": "typescript-plugin-css-modules"}],
    // common start
    "target": "ES2022",
    "moduleResolution": "Node",
    "useDefineForClassFields": true,
    "strict": true,
    "resolveJsonModule": true, 
    "isolatedModules": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "paths": {
      "@/*": ["./src/*"]
    },
    "allowJs": true,
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "sourceMap": true,
    // common end 
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.js"],
  "references": [{ "path": "./tsconfig.node.json" }],
  //eslint-plugin-vue需要升级到9.x版本，同时ts配置文件加上下面的代码，否则<template>标签中的变量会报未定义的错误。
  "vueCompilerOptions": {
    "target": 2.7
  },
}
