module.exports = {
    root: true,
    env: {
      browser: true,
      node: true,
      jest: true,
      es2022: true, // 启动某个版本的全局变量并自动启动该版本的语法
    },
    ignorePatterns: ['.eslintrc.js'],
    // 继承config，内部包含用到的plugin和rule配置，一旦继承代表开启这些rule配置
    extends: [
      'eslint:recommended',
      'plugin:vue/vue3-recommended',// 这是vue2的推荐配置'plugin:vue/recommended',
      'plugin:@typescript-eslint/recommended',
      'prettier', // 使用的是eslint-config-prettier，用于关掉与eslint与prettier冲突的配置
      'plugin:prettier/recommended',
    ],
    // ESLint默认的Parser的作用是将我们写的代码转换为ESTree，ESLint会对ESTree进行校验。ESTree只是一个AST的某一种规范，ESTree本质上还是AST
    parser: 'vue-eslint-parser',
    parserOptions: {
      parser: '@typescript-eslint/parser',
    },
    // 添加其他rule逻辑实现
    plugins: ['vue', '@typescript-eslint/eslint-plugin', 'prettier'],
    // 覆盖或开启rule配置
    rules: {
      eqeqeq: 'error', // 开启严格等于
      "@typescript-eslint/no-empty-function": "off",
      "vue/no-deprecated-filter":"off",
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "vue/multi-word-component-names": "off",
    },
    // 要为特定类型的文件覆盖配置，请使用 overrides
    overrides: [],
  };
  