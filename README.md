# Technology Stack
- [x] Vue2.7
- [x] TypeScript
- [x] Vite
- [x] Vue Composition Api
- [x] Vue Class Component(Not recommended)
- [x] TSX,JSX(Not recommended)
- [x] SASS
- [x] Multiple entry points
- [x] Prerender
- [x] VantUI,NutUI
- [x] Zustand
- [x] vanilla-extract
- [x] Stylelint
- [ ] Tailwind CSS


# TODO


# RUN
- 1.use volta to install Node.js
- 2.use npm to install dependent packages
   ```bash
    npm i
    ```
    
- 3.develop
    ```bash
    npm run dev
    # or dev one module
    npm run devModule --module=xxx
    ```
    
- 4.build
    ```bash
    # build one module:
    npm run buildModule --module=xxx
    # or 
    npm run buildModule --module=xxx --build_type=debug
    # build_type defaults to debug in development mode and to release in production mode
    # or build all modules:
    npm run build
    ```
    
# Multiple entry points + Prerender
- development mode example:
  - http://localhost:5173/src/module/demo/#/second
  - http://localhost:5173/#/second
- production mode example (in child module and prerender disabled):
  - http://127.0.0.1:5500/dist/demo/src/module/demo/#/
  - http://127.0.0.1:5500/dist/demo/src/module/demo/#/second
- production mode example (in child module and prerender enabled):
  - http://127.0.0.1:5500/dist/demo/#/
  - http://127.0.0.1:5500/dist/demo/re-second/#/second
- production mode example (in root module and whether prerender is on or off):
  - http://127.0.0.1:5500/dist/#/
  - http://127.0.0.1:5500/dist/#/second

# NutUI
https://nutui.jd.com/jdl/#/intro

https://nutui.jd.com/2x/#/intro